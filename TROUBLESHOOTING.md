# Troubleshooting Guide

## Common Issues and Solutions

### 1. 307 Redirect Errors

**Problem**: Getting 307 Temporary Redirect errors when making API calls.

**Solution**:
- Use the updated `start-dev.sh` script which automatically unsets proxy settings
- Manually unset proxy variables if needed:
  ```bash
  unset http_proxy https_proxy HTTP_PROXY HTTPS_PROXY ftp_proxy FTP_PROXY no_proxy NO_PROXY
  ```
- For testing API calls manually, always unset proxy first:
  ```bash
  unset http_proxy https_proxy HTTP_PROXY HTTPS_PROXY ftp_proxy FTP_PROXY no_proxy NO_PROXY
  curl -X GET http://************:8000/api/v1/setups/ -H "Content-Type: application/json"
  ```

### 2. Port Already in Use

**Problem**: "Address already in use" error when starting services.

**Solution**:
- The updated `start-dev.sh` script automatically detects and stops conflicting processes
- Manually stop processes if needed:
  ```bash
  ./stop-dev.sh
  # or manually:
  pkill -f "uvicorn.*app.main:app"
  pkill -f "celery.*worker"
  pkill -f "vite"
  ```

### 3. Database Connection Issues

**Problem**: Cannot connect to PostgreSQL database.

**Solution**:
- Ensure PostgreSQL is installed and running:
  ```bash
  sudo systemctl status postgresql
  sudo systemctl start postgresql
  ```
- Check database configuration in `backend/app/core/config.py`

### 4. Redis Connection Issues

**Problem**: Celery cannot connect to Redis.

**Solution**:
- Ensure Redis is installed and running:
  ```bash
  sudo systemctl status redis
  sudo systemctl start redis
  # or start manually:
  redis-server --daemonize yes
  ```

### 5. Frontend Build Issues

**Problem**: Frontend fails to start or build.

**Solution**:
- Clear node_modules and reinstall:
  ```bash
  cd frontend
  rm -rf node_modules package-lock.json
  npm install
  ```

### 6. Python Dependencies Issues

**Problem**: Missing Python packages or import errors.

**Solution**:
- Recreate virtual environment:
  ```bash
  cd backend
  rm -rf venv
  python3 -m venv venv
  source venv/bin/activate
  pip install -r requirements.txt
  ```

## Development Scripts

### Starting Services
```bash
./start-dev.sh
```

### Stopping Services
```bash
./stop-dev.sh
```

### Manual Service Management
```bash
# Start individual services
cd backend && source venv/bin/activate && uvicorn app.main:app --reload
cd frontend && npm run dev
celery -A app.core.celery_app worker --loglevel=info

# Check running processes
ps aux | grep -E "(uvicorn|celery|vite)"
lsof -i :8000  # Check what's using port 8000
lsof -i :3000  # Check what's using port 3000
```

## Environment Variables

The following proxy variables are automatically unset by `start-dev.sh`:
- `http_proxy` / `HTTP_PROXY`
- `https_proxy` / `HTTPS_PROXY`
- `ftp_proxy` / `FTP_PROXY`
- `no_proxy` / `NO_PROXY`

## Service URLs

### Local Access (on the remote server)
- **Backend API**: http://localhost:8000
- **Frontend**: http://localhost:3000
- **API Documentation**: http://localhost:8000/docs
- **API Alternative Docs**: http://localhost:8000/redoc

### Remote Access (from your local machine)
- **Backend API**: http://************:8000
- **Frontend**: http://************:3000
- **API Documentation**: http://************:8000/docs
- **API Alternative Docs**: http://************:8000/redoc

### Remote Access Notes
- The frontend automatically detects when accessed remotely and configures API calls accordingly
- No 307 redirects should occur when using the remote IP addresses
- Proxy settings are automatically unset by the start script to prevent conflicts

## Logs and Debugging

- Backend logs are shown in the terminal where `start-dev.sh` is running
- Frontend logs are also shown in the same terminal
- Celery worker logs are included in the output
- For more detailed logs, run services individually in separate terminals
