{"environment": {"hosts": [{"hosttype": "esx", "hostname": "shelf65sut.esxcert.local", "user": "root", "password": "ca$hc0w"}, {"hosttype": "esx", "hostname": "shelf65aux.esxcert.local", "user": "root", "password": "ca$hc0w"}]}, "numHosts": 2, "params": {"testParams": [{"params_display": {}, "name": "Networking::Functional::NoVM_IPv4Ping", "params": {}}, {"params_display": {}, "name": "Networking::Functional::NoVM_HardwareCheck", "params": {}}, {"params_display": {}, "name": "DDV::Functional::EIRandomNoReload", "params": {}}]}}