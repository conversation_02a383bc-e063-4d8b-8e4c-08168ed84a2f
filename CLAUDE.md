# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Cedori Test Orchestrator is a scalable test orchestration system for VMware NIC Device Driver Certification that manages 40-50 test setups from a single dashboard. The system uses SSH connections to execute tests on VIVa agents via CLI commands and runlist.json files.

## Tech Stack

**Backend**: FastAPI (Python 3.11), PostgreSQL, Redis, Celery, Paramiko (SSH)
**Frontend**: React 18 with TypeScript, Material-UI, Vite

## Common Development Commands

### Quick Start (Docker)
```bash
docker-compose up -d
```

### Local Development
```bash
./start-dev.sh  # Starts all services locally
```

### Backend Commands
```bash
cd backend
source venv/bin/activate  # If using virtual environment
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000  # Start API
celery -A app.core.celery_app worker --loglevel=info     # Start Celery worker
pytest                                                     # Run tests
```

### Frontend Commands
```bash
cd frontend
npm run dev      # Start development server (port 3000)
npm run build    # Production build
npm run lint     # Run ESLint
```

### Testing
```bash
# Backend
cd backend && pytest

# Frontend - No test framework configured yet
```

## Architecture Overview

### Backend Structure
- `app/api/` - REST API endpoints (FastAPI routers)
- `app/core/` - Core configuration and Celery setup
- `app/models/` - SQLAlchemy database models (setup.py, test.py, runlist.py)
- `app/schemas/` - Pydantic schemas for validation
- `app/scrapers/` - SSH-based agent interaction (agent_scraper.py, base_scraper.py)
- `app/services/` - SSH executor service for agent communication
- `app/tasks/` - Celery async tasks

### Key Architectural Decisions
1. **Multi-tenant Architecture**: Each setup has primary and secondary agents
2. **SSH Execution**: Uses Paramiko to execute CLI commands on agents via SSH
3. **Runlist-based Testing**: Tests are executed using runlist.json files
4. **Async Processing**: Celery handles long-running test executions
5. **Real-time Updates**: WebSocket connections for live progress tracking
6. **Test Routing**: ~97% tests run on single host, ~3% require dual-host setup

### Database Models
- `TestSetup`: Complete test environment with 2 agents
- `Agent`: Individual test agent with IP and credentials
- `Test`: Test definitions with type (single/dual host)
- `TestRun`: Collection of test executions
- `TestExecution`: Individual test execution on an agent
- `Runlist`: Test execution configuration and results

### API Structure
- Base URL: `/api/v1`
- Main endpoints: `/setups`, `/test-runs`, `/agents`, `/runlists`
- WebSocket: `/ws/updates` for real-time updates
- API docs available at http://localhost:8000/docs

## Configuration

Environment variables (backend/.env):
```
DATABASE_URL=postgresql://cedori:cedori123@localhost/cedori_db
REDIS_URL=redis://localhost:6379
SECRET_KEY=your-secret-key-here
SSH_TIMEOUT=300
SSH_DEFAULT_PORT=22
```

Key settings in `backend/app/core/config.py`:
- `MAX_CONCURRENT_SSH_CONNECTIONS`: 10
- `MAX_CONCURRENT_TESTS_PER_SETUP`: 2
- `SSH_TIMEOUT`: 300 seconds
- `SSH_DEFAULT_PORT`: 22
- `CORS_ORIGINS`: ["http://localhost:3000", "http://localhost:8000"]

## Important Notes

1. **SSH Access**: Agents must be accessible via SSH (default: root/vmware on port 22)
2. **Database Migrations**: No migration tool configured - models in `app/models/`
3. **SSH Executor**: Central to the system - handles all agent interactions via SSH
4. **Runlist Location**: Agents expect runlists at `/vmware/input/runlist.json`
5. **Frontend Proxy**: Vite config proxies `/api` to backend (see frontend/vite.config.ts)
6. **Task Queue**: Redis required for Celery broker and result backend

## Viva Agent Memories

- main viva agent **************