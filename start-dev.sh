#!/bin/bash

# Development startup script without Docker

# Unset proxy settings to avoid 307 redirect errors
unset http_proxy
unset https_proxy
unset HTTP_PROXY
unset HTTPS_PROXY
unset ftp_proxy
unset FTP_PROXY
unset no_proxy
unset NO_PROXY

# Detect the host IP for remote access
HOST_IP=$(hostname -I | awk '{print $1}')
export HOST=$HOST_IP

echo "Starting development services..."
echo "Proxy settings have been unset to avoid redirect issues."
echo "Host IP detected as: $HOST_IP"
echo "Frontend will be accessible at: http://$HOST_IP:3000"
echo "Backend will be accessible at: http://$HOST_IP:8000"

# Check if Redis is installed
if ! command -v redis-server &> /dev/null; then
    echo "Redis not found. Install with: sudo apt-get install redis-server"
    exit 1
fi

# Check if PostgreSQL is installed  
if ! command -v psql &> /dev/null; then
    echo "PostgreSQL not found. Install with: sudo apt-get install postgresql postgresql-contrib"
    exit 1
fi

# Start Redis
echo "Starting Redis..."
redis-server --daemonize yes

# Create Python virtual environment if not exists
if [ ! -d "backend/venv" ]; then
    echo "Creating Python virtual environment..."
    cd backend
    python3 -m venv venv
    source venv/bin/activate
    pip install -r requirements.txt
    cd ..
fi

# Activate virtual environment
source backend/venv/bin/activate

# Check if port 8000 is already in use
if lsof -Pi :8000 -sTCP:LISTEN -t >/dev/null ; then
    echo "Port 8000 is already in use. Stopping existing processes..."
    pkill -f "uvicorn.*app.main:app" || true
    sleep 2
fi

# Start backend
echo "Starting FastAPI backend..."
cd backend
# Ensure proxy settings are unset for backend
unset http_proxy https_proxy HTTP_PROXY HTTPS_PROXY ftp_proxy FTP_PROXY no_proxy NO_PROXY
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload &
BACKEND_PID=$!

# Wait a moment and check if backend started successfully
sleep 3
if ! kill -0 $BACKEND_PID 2>/dev/null; then
    echo "ERROR: Backend failed to start!"
    exit 1
fi

# Start Celery worker
echo "Starting Celery worker..."
# Ensure proxy settings are unset for Celery
unset http_proxy https_proxy HTTP_PROXY HTTPS_PROXY ftp_proxy FTP_PROXY no_proxy NO_PROXY
celery -A app.core.celery_app worker --loglevel=info &
CELERY_PID=$!

cd ..

# Install frontend dependencies if needed
if [ ! -d "frontend/node_modules" ]; then
    echo "Installing frontend dependencies..."
    cd frontend
    npm install
    cd ..
fi

# Start frontend
echo "Starting React frontend..."
cd frontend
# Ensure proxy settings are unset for frontend
unset http_proxy https_proxy HTTP_PROXY HTTPS_PROXY ftp_proxy FTP_PROXY no_proxy NO_PROXY
# Set HOST environment variable for Vite config
export HOST=$HOST_IP
npm run dev &
FRONTEND_PID=$!

echo ""
echo "Services started:"
echo "Local access:"
echo "- Backend API: http://localhost:8000"
echo "- Frontend: http://localhost:3000"
echo "- API Docs: http://localhost:8000/docs"
echo ""
echo "Remote access:"
echo "- Backend API: http://$HOST_IP:8000"
echo "- Frontend: http://$HOST_IP:3000"
echo "- API Docs: http://$HOST_IP:8000/docs"
echo ""
echo "PIDs: Backend=$BACKEND_PID, Celery=$CELERY_PID, Frontend=$FRONTEND_PID"
echo "To stop all services, run: ./stop-dev.sh"
echo ""
echo "✅ Proxy settings have been unset to prevent 307 redirect errors"
echo "✅ All services are running with proper environment settings"
echo "✅ Frontend configured for remote access via $HOST_IP"

# Keep script running
wait