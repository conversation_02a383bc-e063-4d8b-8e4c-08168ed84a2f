# Cedori Test Orchestrator

A scalable test orchestration system for VMware NIC Device Driver Certification that manages multiple test setups (40-50) from a single dashboard.

## Architecture Overview

### System Components

1. **Backend (FastAPI + Python)**
   - REST API for managing test setups, agents, and test runs
   - WebSocket support for real-time updates
   - PostgreSQL for data persistence
   - Redis for caching and task queue

2. **SSH Executor Service (Paramiko)**
   - Direct SSH access to VIVa agents for test execution
   - Runlist-based test management
   - CLI command execution via AgentLauncher
   - Status monitoring and result collection

3. **Task Queue (Celery + Redis)**
   - Asynchronous test dispatching
   - Agent health monitoring
   - Progress tracking

4. **Frontend Dashboard (React + Material-UI)**
   - Real-time test progress monitoring
   - Multi-setup management (40-50 setups)
   - Test result visualization
   - Runlist editor and management

## Key Features

- **Multi-Setup Management**: Control 40-50 test setups from one dashboard
- **Intelligent Test Dispatch**: Automatically routes tests to appropriate agents
  - ~97% of tests run on single host
  - ~3% require dual-host setup
- **SSH-Based Execution**: Direct CLI access to agents via SSH
- **Runlist Management**: Create, edit, and execute runlists with multiple tests
- **Real-time Updates**: WebSocket connections for live progress tracking
- **Scalable Architecture**: Celery workers can be scaled horizontally

## Quick Start

1. **Clone the repository**
   ```bash
   cd /home/<USER>/projects/cedori-operator
   ```

2. **Start the services**
   ```bash
   docker-compose up -d
   ```

3. **Access the dashboard**
   - Frontend: http://localhost:3000
   - API Docs: http://localhost:8000/docs

## Configuration

### Adding Test Setups

1. Navigate to the Setups page in the dashboard
2. Click "Add Setup"
3. Configure the agents:
   - Primary Agent IP and SSH credentials (default: root/vmware)
   - Secondary Agent IP (for dual-host tests)
   - SSH port (default: 22)

### Environment Variables

Create a `.env` file in the backend directory:

```env
DATABASE_URL=postgresql://cedori:cedori123@localhost/cedori_db
REDIS_URL=redis://localhost:6379
SECRET_KEY=your-secret-key-here
SSH_TIMEOUT=300
SSH_DEFAULT_PORT=22
```

## SSH Agent Access

The system connects to VIVa agents via SSH to execute tests:

- **Default credentials**: root/vmware
- **Runlist location**: /vmware/input/runlist.json
- **CLI command**: `AgentLauncher -r /vmware/input/runlist.json -e -t "Test1" "Test2"`

## Runlist Management

### Creating a Runlist

Runlists define test execution parameters and can be created via:

1. **API**: POST to `/api/v1/runlists`
2. **Templates**: Use predefined templates for common test scenarios
3. **Frontend**: Use the runlist editor (coming soon)

### Runlist Structure

```json
{
  "tests": [
    {
      "name": "ping_test",
      "enabled": true,
      "type": "single_host"
    }
  ],
  "parameters": {
    "nfs_server": "********",
    "timeout": 300
  }
}
```

### Available Templates

- **Basic Connectivity Tests**: Network connectivity and basic functionality
- **Performance Test Suite**: Throughput and latency testing
- **VMware NIC Certification**: Full certification test suite

## Development

### Backend Development
```bash
cd backend
pip install -r requirements.txt
uvicorn app.main:app --reload
```

### Frontend Development
```bash
cd frontend
npm install
npm run dev
```

### Running Tests
```bash
cd backend
pytest
```

## Database Schema

- **TestSetup**: Represents a complete test environment (2 agents)
- **Agent**: Individual test agent with IP, credentials, and capabilities
- **Test**: Test definitions with type (single/dual host)
- **TestRun**: A collection of test executions
- **TestExecution**: Individual test execution on an agent
- **Runlist**: Test execution configuration and results

## API Endpoints

### Setup Management
- `GET /api/v1/setups` - List all test setups
- `POST /api/v1/setups` - Create new setup
- `GET /api/v1/setups/{id}` - Get setup details

### Test Execution
- `POST /api/v1/test-runs` - Start new test run
- `GET /api/v1/test-runs/{id}` - Get test run progress

### Runlist Management
- `GET /api/v1/runlists` - List all runlists
- `POST /api/v1/runlists` - Create new runlist
- `GET /api/v1/runlists/{id}` - Get runlist details
- `POST /api/v1/runlists/{id}/execute` - Execute a runlist
- `GET /api/v1/runlists/templates` - Get available templates

### Real-time Updates
- `WS /api/v1/ws/updates` - WebSocket for real-time updates

## Testing SSH Connection

To test SSH connectivity to an agent:

```python
import paramiko
ssh = paramiko.SSHClient()
ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
ssh.connect('agent-ip', username='root', password='vmware')
stdin, stdout, stderr = ssh.exec_command('echo "SSH connection successful"')
print(stdout.read().decode())
```

## Migration from Web Scraping

The system has been migrated from Playwright-based web scraping to SSH-based execution:

- **Old approach**: Automated browser interactions with agent web GUIs
- **New approach**: Direct SSH access for CLI command execution
- **Benefits**: More reliable, faster, resource-efficient, better error handling

## Next Steps

1. Add agent IPs and SSH credentials via the dashboard
2. Create or import runlists for your test scenarios
3. Execute runlists and monitor progress in real-time
4. View consolidated results across all setups

# Cedori Operator

This project consists of a backend API (Python with FastAPI) and a frontend application (TypeScript with Vite/React).

## Project Structure

- `backend/`: Contains the FastAPI application, database migrations, and Python dependencies.
- `frontend/`: Contains the Vite/React application and its dependencies.
- `docs/`: Documentation for the project.
- `runlists/`: Contains various runlist files.
- `docker-compose.yml`: Defines the multi-service Docker environment.

## Getting Started

### Prerequisites

- Docker and Docker Compose (if you plan to use Docker)
- Python 3.9+ (for backend development)
- Node.js (LTS recommended) and npm/yarn (for frontend development)

### Backend Setup

1. Navigate to the `backend` directory:
   ```bash
   cd backend
   ```
2. Create a virtual environment and install dependencies:
   ```bash
   python3 -m venv venv
   source venv/bin/activate
   pip install -r requirements.txt
   ```
3. Run database migrations (if applicable):
   ```bash
   alembic upgrade head
   ```
4. Start the backend server:
   ```bash
   uvicorn app.main:app --reload
   ```

### Frontend Setup

1. Navigate to the `frontend` directory:
   ```bash
   cd frontend
   ```
2. Install Node.js dependencies:
   ```bash
   npm install
   # or yarn install
   ```
3. Start the frontend development server:
   ```bash
   npm run dev
   # or yarn dev
   ```

### Docker Compose

To run the entire application using Docker Compose:

```bash
docker-compose up --build
```