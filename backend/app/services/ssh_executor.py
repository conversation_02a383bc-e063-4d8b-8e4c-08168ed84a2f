import asyncio
import json
import logging
from typing import Dict, List, Optional, Any
from pathlib import Path
import tempfile
from datetime import datetime

import paramiko
from paramiko import SSHClient, AutoAddPolicy
from paramiko.ssh_exception import SSHException, AuthenticationException

logger = logging.getLogger(__name__)


class SSHExecutor:
    """SSH executor for VIVa agent commands and runlist management."""
    
    def __init__(self, hostname: str, username: str = "root", password: str = "vmware", port: int = 22):
        self.hostname = hostname
        self.username = username
        self.password = password
        self.port = port
        self.client: Optional[SSHClient] = None
        self.sftp = None
        
    def __enter__(self):
        self.connect()
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.disconnect()
        
    def connect(self) -> None:
        """Establish SSH connection to the agent."""
        try:
            self.client = SSHClient()
            self.client.set_missing_host_key_policy(AutoAddPolicy())
            self.client.connect(
                hostname=self.hostname,
                port=self.port,
                username=self.username,
                password=self.password,
                timeout=30,
                look_for_keys=False,
                allow_agent=False
            )
            self.sftp = self.client.open_sftp()
            logger.info(f"SSH connection established to {self.hostname}")
        except AuthenticationException:
            logger.error(f"Authentication failed for {self.hostname}")
            raise
        except SSHException as e:
            logger.error(f"SSH connection failed to {self.hostname}: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error connecting to {self.hostname}: {e}")
            raise
            
    def disconnect(self) -> None:
        """Close SSH connection."""
        if self.sftp:
            self.sftp.close()
            self.sftp = None
        if self.client:
            self.client.close()
            self.client = None
            logger.info(f"SSH connection closed to {self.hostname}")
            
    def execute_command(self, command: str, timeout: int = 300) -> tuple[str, str, int]:
        """Execute a command on the remote host.
        
        Args:
            command: Command to execute
            timeout: Command timeout in seconds
            
        Returns:
            Tuple of (stdout, stderr, exit_code)
        """
        if not self.client:
            raise RuntimeError("SSH client not connected")
            
        try:
            stdin, stdout, stderr = self.client.exec_command(command, timeout=timeout)
            stdout_data = stdout.read().decode('utf-8')
            stderr_data = stderr.read().decode('utf-8')
            exit_code = stdout.channel.recv_exit_status()
            
            if exit_code != 0:
                logger.warning(f"Command '{command}' returned non-zero exit code: {exit_code}")
                logger.warning(f"stderr: {stderr_data}")
                
            return stdout_data, stderr_data, exit_code
        except Exception as e:
            logger.error(f"Error executing command '{command}': {e}")
            raise
            
    def upload_file(self, local_path: str, remote_path: str) -> None:
        """Upload a file to the remote host.
        
        Args:
            local_path: Path to local file
            remote_path: Path on remote host
        """
        if not self.sftp:
            raise RuntimeError("SFTP client not connected")
            
        try:
            self.sftp.put(local_path, remote_path)
            logger.info(f"Uploaded {local_path} to {self.hostname}:{remote_path}")
        except Exception as e:
            logger.error(f"Error uploading file: {e}")
            raise
            
    def download_file(self, remote_path: str, local_path: str) -> None:
        """Download a file from the remote host.
        
        Args:
            remote_path: Path on remote host
            local_path: Path to save locally
        """
        if not self.sftp:
            raise RuntimeError("SFTP client not connected")
            
        try:
            self.sftp.get(remote_path, local_path)
            logger.info(f"Downloaded {self.hostname}:{remote_path} to {local_path}")
        except Exception as e:
            logger.error(f"Error downloading file: {e}")
            raise
            
    def upload_runlist(self, runlist_data: Dict[str, Any], remote_path: str = "/vmware/input/runlist.json") -> None:
        """Upload a runlist to the agent.
        
        Args:
            runlist_data: Runlist dictionary
            remote_path: Path to save runlist on agent
        """
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as tmp:
            json.dump(runlist_data, tmp, indent=2)
            tmp_path = tmp.name
            
        try:
            self.upload_file(tmp_path, remote_path)
            Path(tmp_path).unlink()
        except Exception as e:
            Path(tmp_path).unlink()
            raise
            
    def execute_tests(self, test_names: List[str], runlist_path: str = "/vmware/input/runlist.json") -> str:
        """Execute tests using AgentLauncher CLI.
        
        Args:
            test_names: List of test names to execute
            runlist_path: Path to runlist on agent
            
        Returns:
            Command output
        """
        if not test_names:
            raise ValueError("No tests specified for execution")
            
        # Build command with test names. Test names should go one after another: "test1" "test2"
        test_args = ' '.join(f'"{test}"' for test in test_names)
        command = f"AgentLauncher -e -t {test_args}"
        
        logger.info(f"Executing tests: {command}")
        stdout, stderr, exit_code = self.execute_command(command, timeout=3600)  # 1 hour timeout for test execution
        
        if exit_code != 0:
            # Log but don't raise - VIVa may return non-zero for partial failures
            logger.warning(f"Test execution returned exit code {exit_code}: {stderr}")
            
        return stdout
        
    def get_runlist_results(self, remote_path: str = "/vmware/input/runlist.json") -> Dict[str, Any]:
        """Download and parse runlist results.
        
        Args:
            remote_path: Path to runlist on agent
            
        Returns:
            Parsed runlist with results
        """
        with tempfile.NamedTemporaryFile(mode='r', suffix='.json', delete=False) as tmp:
            tmp_path = tmp.name
            
        try:
            self.download_file(remote_path, tmp_path)
            with open(tmp_path, 'r') as f:
                results = json.load(f)
            Path(tmp_path).unlink()
            return results
        except Exception as e:
            Path(tmp_path).unlink()
            raise
            
    def check_test_status(self) -> Dict[str, Any]:
        """Check if tests are currently running.
        
        Returns:
            Status information including running processes
        """
        # Check for AgentLauncher processes
        stdout, _, _ = self.execute_command("ps aux | grep -E 'AgentLauncher|viva' | grep -v grep")
        
        status = {
            "timestamp": datetime.utcnow().isoformat(),
            "is_running": bool(stdout.strip()),
            "processes": stdout.strip().split('\n') if stdout.strip() else []
        }
        
        # Get system status
        cpu_info, _, _ = self.execute_command("top -bn1 | grep 'Cpu(s)' | head -1")
        mem_info, _, _ = self.execute_command("free -m | grep 'Mem:' | awk '{print $3\"M used of \"$2\"M\"}'")
        
        status["cpu_info"] = cpu_info.strip()
        status["memory_info"] = mem_info.strip()
        
        return status
        
    def get_agent_info(self) -> Dict[str, Any]:
        """Get agent system information.
        
        Returns:
            Dictionary with system information
        """
        info = {}
        
        # Get hostname
        stdout, _, _ = self.execute_command("hostname")
        info["hostname"] = stdout.strip()
        
        # Get OS info
        stdout, _, _ = self.execute_command("cat /etc/os-release | grep PRETTY_NAME | cut -d'=' -f2 | tr -d '\"'")
        info["os"] = stdout.strip()
        
        # Get uptime
        stdout, _, _ = self.execute_command("uptime -p")
        info["uptime"] = stdout.strip()
        
        # Get disk usage
        stdout, _, _ = self.execute_command("df -h / | tail -1 | awk '{print $5\" used (\"$3\" of \"$2\")\"}'")
        info["disk_usage"] = stdout.strip()
        
        # Check for VIVa agent
        stdout, _, _ = self.execute_command("which AgentLauncher 2>/dev/null")
        info["viva_agent_installed"] = bool(stdout.strip())
        
        return info
        
    def list_available_tests(self) -> List[str]:
        """List available tests on the agent.
        
        Returns:
            List of test names
        """
        # This would need to be implemented based on how tests are discovered
        # For now, return empty list
        logger.warning("Test discovery not yet implemented")
        return []


class AsyncSSHExecutor(SSHExecutor):
    """Async wrapper for SSH executor."""
    
    async def connect_async(self) -> None:
        """Establish SSH connection asynchronously."""
        await asyncio.get_event_loop().run_in_executor(None, self.connect)
        
    async def disconnect_async(self) -> None:
        """Close SSH connection asynchronously."""
        await asyncio.get_event_loop().run_in_executor(None, self.disconnect)
        
    async def execute_command_async(self, command: str, timeout: int = 300) -> tuple[str, str, int]:
        """Execute command asynchronously."""
        return await asyncio.get_event_loop().run_in_executor(
            None, self.execute_command, command, timeout
        )
        
    async def upload_runlist_async(self, runlist_data: Dict[str, Any], remote_path: str = "/vmware/input/runlist.json") -> None:
        """Upload runlist asynchronously."""
        await asyncio.get_event_loop().run_in_executor(
            None, self.upload_runlist, runlist_data, remote_path
        )
        
    async def execute_tests_async(self, test_names: List[str], runlist_path: str = "/vmware/input/runlist.json") -> str:
        """Execute tests asynchronously."""
        return await asyncio.get_event_loop().run_in_executor(
            None, self.execute_tests, test_names, runlist_path
        )
        
    async def get_runlist_results_async(self, remote_path: str = "/vmware/input/runlist.json") -> Dict[str, Any]:
        """Get runlist results asynchronously."""
        return await asyncio.get_event_loop().run_in_executor(
            None, self.get_runlist_results, remote_path
        )
        
    async def check_test_status_async(self) -> Dict[str, Any]:
        """Check test status asynchronously."""
        return await asyncio.get_event_loop().run_in_executor(
            None, self.check_test_status
        )
        
    async def get_agent_info_async(self) -> Dict[str, Any]:
        """Get agent info asynchronously."""
        return await asyncio.get_event_loop().run_in_executor(
            None, self.get_agent_info
        )