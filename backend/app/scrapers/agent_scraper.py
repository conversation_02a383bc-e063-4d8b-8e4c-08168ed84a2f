from typing import Dict, Any, List, Optional
import asyncio
import logging
from datetime import datetime
import json
import re

from app.scrapers.base_scraper import BaseScraper
from app.models import TestStatus

logger = logging.getLogger(__name__)


class VMwareAgentScraper(BaseScraper):
    """VMware VIVa agent scraper using SSH and CLI commands."""
    
    async def upload_runlist(self, runlist_data: Dict[str, Any]) -> None:
        """Upload runlist to the agent."""
        try:
            await self.ssh_executor.upload_runlist_async(runlist_data)
            logger.info(f"Successfully uploaded runlist to {self.agent_ip}")
        except Exception as e:
            logger.error(f"Failed to upload runlist: {e}")
            raise
            
    async def execute_tests(self, test_names: List[str]) -> str:
        """Execute tests on the agent using AgentLauncher CLI."""
        try:
            output = await self.ssh_executor.execute_tests_async(test_names)
            logger.info(f"Successfully started {len(test_names)} tests on {self.agent_ip}")
            return output
        except Exception as e:
            logger.error(f"Failed to execute tests: {e}")
            raise
            
    async def get_test_status(self) -> Dict[str, Any]:
        """Get current test execution status."""
        try:
            status = await self.ssh_executor.check_test_status_async()
            
            # Parse running processes to determine active tests
            if status.get('is_running'):
                # Extract test names from process list if possible
                processes = status.get('processes', [])
                active_tests = []
                for process in processes:
                    # Try to extract test names from command line
                    match = re.search(r'-t\s+(.+?)(?:\s+-|$)', process)
                    if match:
                        test_str = match.group(1)
                        # Parse quoted test names
                        tests = re.findall(r'"([^"]+)"', test_str)
                        active_tests.extend(tests)
                status['active_tests'] = active_tests
                
            return status
        except Exception as e:
            logger.error(f"Failed to get test status: {e}")
            return {
                'timestamp': datetime.utcnow().isoformat(),
                'is_running': False,
                'error': str(e)
            }
            
    async def get_test_results(self) -> Dict[str, Any]:
        """Get test results from runlist."""
        try:
            results = await self.ssh_executor.get_runlist_results_async()
            
            # Process results to extract test statuses
            processed_results = {
                'timestamp': datetime.utcnow().isoformat(),
                'runlist': results,
                'tests': []
            }
            
            # VIVa runlist structure: tests are in params.testParams
            params = results.get('params', {})
            test_params = params.get('testParams', [])
            
            # Extract test definitions from VIVa structure
            for test_param in test_params:
                test_name = test_param.get('name', 'Unknown')
                
                # Skip parameter configuration entries
                if test_name.startswith('Test::') and '::Parameters' in test_name:
                    continue
                    
                test_result = {
                    'name': test_name,
                    'status': self._parse_test_status(test_param.get('status', 'pending')),
                    'start_time': test_param.get('start_time'),
                    'end_time': test_param.get('end_time'),
                    'duration': test_param.get('duration'),
                    'output': test_param.get('output', ''),
                    'error': test_param.get('error'),
                    'result': test_param.get('result', {}),
                    'params': test_param.get('params', {})
                }
                processed_results['tests'].append(test_result)
            
            # Also check for test results in a different location if present
            if 'tests' in results:
                for test in results['tests']:
                    test_result = {
                        'name': test.get('name', 'Unknown'),
                        'status': self._parse_test_status(test.get('status', 'pending')),
                        'start_time': test.get('start_time'),
                        'end_time': test.get('end_time'),
                        'duration': test.get('duration'),
                        'output': test.get('output', ''),
                        'error': test.get('error'),
                        'result': test.get('result', {})
                    }
                    processed_results['tests'].append(test_result)
                    
            return processed_results
        except Exception as e:
            logger.error(f"Failed to get test results: {e}")
            raise
            
    async def get_agent_status(self) -> Dict[str, Any]:
        """Get agent system status."""
        try:
            info = await self.ssh_executor.get_agent_info_async()
            
            # Get additional status information
            status = {
                'online': True,
                'hostname': info.get('hostname'),
                'os': info.get('os'),
                'uptime': info.get('uptime'),
                'disk_usage': info.get('disk_usage'),
                'viva_agent_installed': info.get('viva_agent_installed', False),
                'last_check': datetime.utcnow().isoformat()
            }
            
            # Get CPU and memory from test status
            test_status = await self.get_test_status()
            status['cpu_info'] = test_status.get('cpu_info')
            status['memory_info'] = test_status.get('memory_info')
            status['tests_running'] = test_status.get('is_running', False)
            
            return status
        except Exception as e:
            logger.error(f"Failed to get agent status: {e}")
            return {
                'online': False,
                'error': str(e),
                'last_check': datetime.utcnow().isoformat()
            }
            
    async def list_available_tests(self) -> List[str]:
        """List available tests from the VIVa runlist."""
        try:
            # Get the runlist to extract available tests
            runlist = await self.ssh_executor.get_runlist_results_async()
            
            tests = []
            
            # VIVa runlist structure: tests are in params.testParams
            params = runlist.get('params', {})
            test_params = params.get('testParams', [])
            
            for test_param in test_params:
                test_name = test_param.get('name', '')
                
                # Skip empty names and parameter configuration entries
                if not test_name:
                    continue
                    
                # Skip parameter-only entries (these end with ::Parameters)
                if '::Parameters' in test_name:
                    continue
                    
                # Add actual test classes
                tests.append(test_name)
            
            # Sort and deduplicate
            tests = sorted(list(set(tests)))
            
            logger.info(f"Found {len(tests)} available tests in VIVa runlist")
            return tests
            
        except Exception as e:
            logger.error(f"Failed to list available tests: {e}")
            return []
            
    def _parse_test_status(self, status_str: str) -> TestStatus:
        """Parse test status string to TestStatus enum."""
        if not status_str:
            return TestStatus.PENDING
            
        status_str = status_str.lower().strip()
        
        if status_str in ['pending', 'queued', 'waiting']:
            return TestStatus.PENDING
        elif status_str in ['running', 'executing', 'in_progress']:
            return TestStatus.RUNNING
        elif status_str in ['passed', 'success', 'completed']:
            return TestStatus.PASSED
        elif status_str in ['failed', 'failure', 'error']:
            return TestStatus.FAILED
        elif status_str in ['skipped', 'omitted']:
            return TestStatus.SKIPPED
        else:
            return TestStatus.ERROR
            
    async def create_runlist_from_template(self, template: Dict[str, Any], test_names: List[str]) -> Dict[str, Any]:
        """Create a runlist from template with specified tests.
        
        Args:
            template: Base runlist template
            test_names: List of test names to include
            
        Returns:
            Complete runlist ready for execution
        """
        runlist = template.copy()
        
        # Ensure tests section exists
        if 'tests' not in runlist:
            runlist['tests'] = []
            
        # Add tests to runlist
        for test_name in test_names:
            test_entry = {
                'name': test_name,
                'status': 'pending',
                'enabled': True
            }
            runlist['tests'].append(test_entry)
            
        # Add metadata
        runlist['metadata'] = {
            'created_at': datetime.utcnow().isoformat(),
            'total_tests': len(test_names),
            'agent_ip': self.agent_ip
        }
        
        return runlist
        
    async def parse_viva_runlist(self) -> Dict[str, Any]:
        """Parse VIVa runlist structure to extract test information.
        
        Returns:
            Dictionary with test categories and parameter groups
        """
        try:
            runlist = await self.ssh_executor.get_runlist_results_async()
            
            result = {
                'environment': runlist.get('environment', {}),
                'numHosts': runlist.get('numHosts', 0),
                'test_categories': {},
                'parameter_groups': [],
                'total_tests': 0
            }
            
            # Extract parameter groups and tests
            params = runlist.get('params', {})
            test_params = params.get('testParams', [])
            
            tests = []
            for test_param in test_params:
                test_name = test_param.get('name', '')
                
                if not test_name:
                    continue
                    
                # Separate parameter groups from actual tests
                if '::Parameters' in test_name:
                    result['parameter_groups'].append({
                        'name': test_name,
                        'params': test_param.get('params', {}),
                        'params_display': test_param.get('params_display', {})
                    })
                else:
                    tests.append(test_name)
                    
                    # Categorize tests
                    if '::' in test_name:
                        category = test_name.split('::')[0]
                        if category not in result['test_categories']:
                            result['test_categories'][category] = []
                        result['test_categories'][category].append(test_name)
            
            result['total_tests'] = len(tests)
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to parse VIVa runlist: {e}")
            raise
            
    async def get_test_categories(self) -> Dict[str, List[str]]:
        """Get test categories from VIVa runlist.
        
        Returns:
            Dictionary mapping category names to list of tests
        """
        parsed = await self.parse_viva_runlist()
        return parsed['test_categories']
        
    async def get_parameter_groups(self) -> List[Dict[str, Any]]:
        """Get parameter configuration groups from VIVa runlist.
        
        Returns:
            List of parameter group definitions
        """
        parsed = await self.parse_viva_runlist()
        return parsed['parameter_groups']