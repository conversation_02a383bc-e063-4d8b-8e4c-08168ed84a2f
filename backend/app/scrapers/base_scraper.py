from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
import asyncio
import logging
from datetime import datetime

from app.core.config import settings
from app.models import TestStatus
from app.services.ssh_executor import AsyncSSHExecutor

logger = logging.getLogger(__name__)


class BaseScraper(ABC):
    """Base class for agent scrapers, now using SSH instead of web scraping."""
    
    def __init__(self, agent_ip: str, agent_port: int = 22, 
                 username: str = "root", password: str = "vmware"):
        self.agent_ip = agent_ip
        self.agent_port = agent_port
        self.username = username
        self.password = password
        self.ssh_executor: Optional[AsyncSSHExecutor] = None
        
    async def __aenter__(self):
        await self.start()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.stop()
        
    async def start(self):
        """Initialize SSH connection to the agent."""
        self.ssh_executor = AsyncSSHExecutor(
            hostname=self.agent_ip,
            username=self.username,
            password=self.password,
            port=self.agent_port
        )
        await self.ssh_executor.connect_async()
        logger.info(f"SSH connection established to {self.agent_ip}")
        
    async def stop(self):
        """Close SSH connection."""
        if self.ssh_executor:
            await self.ssh_executor.disconnect_async()
            self.ssh_executor = None
            
    @abstractmethod
    async def upload_runlist(self, runlist_data: Dict[str, Any]) -> None:
        """Upload runlist to the agent."""
        pass
        
    @abstractmethod
    async def execute_tests(self, test_names: List[str]) -> str:
        """Execute tests on the agent."""
        pass
        
    @abstractmethod
    async def get_test_status(self) -> Dict[str, Any]:
        """Get current test execution status."""
        pass
        
    @abstractmethod
    async def get_test_results(self) -> Dict[str, Any]:
        """Get test results from runlist."""
        pass
        
    @abstractmethod
    async def get_agent_status(self) -> Dict[str, Any]:
        """Get agent system status."""
        pass
        
    @abstractmethod
    async def list_available_tests(self) -> List[str]:
        """List available tests on the agent."""
        pass
        
    async def check_connection(self) -> bool:
        """Check if SSH connection is active."""
        try:
            stdout, _, exit_code = await self.ssh_executor.execute_command_async("echo 'connection_test'", timeout=5)
            return exit_code == 0 and 'connection_test' in stdout
        except Exception as e:
            logger.error(f"Connection check failed: {e}")
            return False
            
    async def execute_command(self, command: str, timeout: int = 300) -> tuple[str, str, int]:
        """Execute a command on the agent.
        
        Args:
            command: Command to execute
            timeout: Command timeout in seconds
            
        Returns:
            Tuple of (stdout, stderr, exit_code)
        """
        if not self.ssh_executor:
            raise RuntimeError("SSH executor not initialized")
        return await self.ssh_executor.execute_command_async(command, timeout)