from pydantic import BaseModel
from typing import Optional, List
from enum import Enum


class TestType(str, Enum):
    SINGLE_HOST = "single_host"
    DUAL_HOST = "dual_host"


class TestBase(BaseModel):
    name: str
    description: Optional[str] = None
    test_type: TestType = TestType.SINGLE_HOST
    category: Optional[str] = None
    estimated_duration: Optional[int] = None
    priority: int = 5
    tags: List[str] = []


class TestCreate(TestBase):
    pass


class TestUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    test_type: Optional[TestType] = None
    category: Optional[str] = None
    estimated_duration: Optional[int] = None
    priority: Optional[int] = None
    tags: Optional[List[str]] = None


class Test(TestBase):
    id: int
    
    class Config:
        from_attributes = True