from pydantic import BaseModel
from typing import Optional, List
from datetime import datetime
from enum import Enum


class TestStatus(str, Enum):
    PENDING = "pending"
    RUNNING = "running"
    PASSED = "passed"
    FAILED = "failed"
    SKIPPED = "skipped"
    ERROR = "error"


class TestRunBase(BaseModel):
    name: str
    setup_id: int


class TestRunCreate(TestRunBase):
    pass


class TestRun(TestRunBase):
    id: int
    status: TestStatus = TestStatus.PENDING
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    total_tests: int = 0
    passed_tests: int = 0
    failed_tests: int = 0
    skipped_tests: int = 0
    progress_percentage: float = 0.0
    
    class Config:
        from_attributes = True