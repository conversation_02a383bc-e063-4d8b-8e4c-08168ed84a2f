from pydantic import BaseModel
from typing import Optional, Dict, Any
from datetime import datetime


class AgentBase(BaseModel):
    name: str
    ip_address: str
    port: int = 80
    username: Optional[str] = None
    password: Optional[str] = None
    is_primary: bool = True


class AgentCreate(AgentBase):
    setup_id: int


class AgentUpdate(BaseModel):
    name: Optional[str] = None
    ip_address: Optional[str] = None
    port: Optional[int] = None
    username: Optional[str] = None
    password: Optional[str] = None
    is_primary: Optional[bool] = None
    is_active: Optional[bool] = None


class AgentResponse(AgentBase):
    id: int
    setup_id: int
    is_active: bool
    last_seen: Optional[datetime] = None
    capabilities: Dict[str, Any] = {}
    
    class Config:
        from_attributes = True


class AgentStatus(BaseModel):
    id: int
    name: str
    online: bool
    version: Optional[str] = None
    uptime: Optional[str] = None
    cpu_usage: Optional[float] = None
    memory_usage: Optional[float] = None
    active_tests: int = 0
    queued_tests: int = 0
    last_check: str
    error: Optional[str] = None