from pydantic import BaseModel, <PERSON>
from typing import Dict, Any, List, Optional
from datetime import datetime
from enum import Enum


class RunlistStatus(str, Enum):
    DRAFT = "draft"
    PENDING = "pending"
    EXECUTING = "executing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TestEntry(BaseModel):
    """Individual test entry in a runlist."""
    name: str
    enabled: bool = True
    type: Optional[str] = "single_host"
    parameters: Optional[Dict[str, Any]] = {}


class RunlistContent(BaseModel):
    """Runlist content structure."""
    tests: List[TestEntry]
    parameters: Dict[str, Any] = Field(default_factory=dict, description="Global parameters for all tests")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict)


class RunlistBase(BaseModel):
    """Base runlist schema."""
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    content: RunlistContent
    is_template: bool = False


class RunlistCreate(RunlistBase):
    """Schema for creating a runlist."""
    setup_id: Optional[int] = None


class RunlistUpdate(BaseModel):
    """Schema for updating a runlist."""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    content: Optional[RunlistContent] = None
    status: Optional[RunlistStatus] = None


class RunlistInDB(RunlistBase):
    """Schema for runlist from database."""
    id: int
    status: RunlistStatus
    setup_id: Optional[int]
    test_run_id: Optional[int]
    results: Optional[Dict[str, Any]]
    created_at: datetime
    updated_at: datetime
    executed_at: Optional[datetime]
    completed_at: Optional[datetime]
    
    class Config:
        from_attributes = True


class RunlistSummary(BaseModel):
    """Summary of runlist execution."""
    id: int
    name: str
    status: RunlistStatus
    total_tests: int
    passed_tests: int
    failed_tests: int
    skipped_tests: int
    error_tests: int
    progress_percentage: float
    
    class Config:
        from_attributes = True


class RunlistExecute(BaseModel):
    """Schema for executing a runlist."""
    test_names: Optional[List[str]] = Field(None, description="Specific tests to execute. If None, all enabled tests are executed")
    parameters_override: Optional[Dict[str, Any]] = Field(None, description="Override global parameters")
    
    
class RunlistTemplate(BaseModel):
    """Predefined runlist template."""
    name: str
    description: str
    category: str
    content: RunlistContent
    

class RunlistTemplateList(BaseModel):
    """List of available templates."""
    templates: List[RunlistTemplate]