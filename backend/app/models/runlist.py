from sqlalchemy import Column, Integer, String, DateTime, JSO<PERSON>, Enum as S<PERSON><PERSON><PERSON>, Foreign<PERSON>ey, Text, Boolean
from sqlalchemy.orm import relationship
from datetime import datetime
import enum

from app.db.base import Base


class RunlistStatus(enum.Enum):
    DRAFT = "draft"
    PENDING = "pending"
    EXECUTING = "executing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class Runlist(Base):
    __tablename__ = "runlists"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    
    # Runlist template or actual runlist content
    content = Column(JSON, nullable=False)
    
    # Status tracking
    status = Column(SQLEnum(RunlistStatus), default=RunlistStatus.DRAFT)
    
    # If this is a template
    is_template = Column(Boolean, default=False)
    
    # Foreign keys
    setup_id = Column(Integer, ForeignKey("test_setups.id"), nullable=True)
    test_run_id = Column(<PERSON><PERSON><PERSON>, <PERSON><PERSON>ey("test_runs.id"), nullable=True)
    
    # Results after execution
    results = Column(JSON, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    executed_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    
    # Relationships
    setup = relationship("TestSetup", back_populates="runlists")
    test_run = relationship("TestRun", back_populates="runlist", uselist=False)
    
    def to_dict(self):
        """Convert runlist to dictionary."""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "content": self.content,
            "status": self.status.value if self.status else None,
            "is_template": self.is_template,
            "setup_id": self.setup_id,
            "test_run_id": self.test_run_id,
            "results": self.results,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "executed_at": self.executed_at.isoformat() if self.executed_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
        }
        
    def get_test_names(self):
        """Extract test names from runlist content."""
        if not self.content or 'tests' not in self.content:
            return []
        return [test['name'] for test in self.content['tests'] if test.get('enabled', True)]
        
    def update_test_result(self, test_name: str, result: dict):
        """Update result for a specific test."""
        if not self.results:
            self.results = {"tests": []}
            
        # Find or create test result entry
        test_found = False
        for test in self.results.get("tests", []):
            if test.get("name") == test_name:
                test.update(result)
                test_found = True
                break
                
        if not test_found:
            result["name"] = test_name
            self.results["tests"].append(result)
            
    def get_summary(self):
        """Get execution summary."""
        if not self.results or 'tests' not in self.results:
            return {
                "total": 0,
                "passed": 0,
                "failed": 0,
                "skipped": 0,
                "error": 0
            }
            
        tests = self.results['tests']
        return {
            "total": len(tests),
            "passed": sum(1 for t in tests if t.get('status') == 'passed'),
            "failed": sum(1 for t in tests if t.get('status') == 'failed'),
            "skipped": sum(1 for t in tests if t.get('status') == 'skipped'),
            "error": sum(1 for t in tests if t.get('status') == 'error')
        }