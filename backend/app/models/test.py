from sqlalchemy import Column, Integer, String, DateTime, Boolean, Foreign<PERSON>ey, Enum, Float, Text, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime
import enum

from app.db.base import Base


class TestStatus(str, enum.Enum):
    PENDING = "pending"
    RUNNING = "running"
    PASSED = "passed"
    FAILED = "failed"
    SKIPPED = "skipped"
    ERROR = "error"


class TestType(str, enum.Enum):
    SINGLE_HOST = "single_host"
    DUAL_HOST = "dual_host"


class Test(Base):
    __tablename__ = "tests"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, unique=True, index=True)
    description = Column(Text, nullable=True)
    test_type = Column(Enum(TestType), default=TestType.SINGLE_HOST)
    category = Column(String, nullable=True)
    estimated_duration = Column(Integer)
    priority = Column(Integer, default=5)
    tags = Column(JSON, default=list)
    
    test_executions = relationship("TestExecution", back_populates="test")


class TestRun(Base):
    __tablename__ = "test_runs"
    
    id = Column(Integer, primary_key=True, index=True)
    setup_id = Column(Integer, ForeignKey("test_setups.id"))
    name = Column(String)
    status = Column(Enum(TestStatus), default=TestStatus.PENDING)
    started_at = Column(DateTime(timezone=True), nullable=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)
    total_tests = Column(Integer, default=0)
    passed_tests = Column(Integer, default=0)
    failed_tests = Column(Integer, default=0)
    skipped_tests = Column(Integer, default=0)
    progress_percentage = Column(Float, default=0.0)
    
    setup = relationship("TestSetup", back_populates="test_runs")
    test_executions = relationship("TestExecution", back_populates="test_run", cascade="all, delete-orphan")
    runlist = relationship("Runlist", back_populates="test_run", uselist=False)


class TestExecution(Base):
    __tablename__ = "test_executions"
    
    id = Column(Integer, primary_key=True, index=True)
    test_id = Column(Integer, ForeignKey("tests.id"))
    test_run_id = Column(Integer, ForeignKey("test_runs.id"))
    agent_id = Column(Integer, ForeignKey("agents.id"))
    status = Column(Enum(TestStatus), default=TestStatus.PENDING)
    started_at = Column(DateTime(timezone=True), nullable=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)
    duration_seconds = Column(Float, nullable=True)
    error_message = Column(Text, nullable=True)
    log_output = Column(Text, nullable=True)
    result_data = Column(JSON, default=dict)
    
    test = relationship("Test", back_populates="test_executions")
    test_run = relationship("TestRun", back_populates="test_executions")
    agent = relationship("Agent", back_populates="test_executions")