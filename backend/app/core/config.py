from pydantic_settings import BaseSettings
from typing import List, Optional


class Settings(BaseSettings):
    PROJECT_NAME: str = "Cedori Test Orchestrator"
    VERSION: str = "1.0.0"
    API_V1_STR: str = "/api/v1"
    
    DATABASE_URL: str = "sqlite:///./cedori.db"
    
    REDIS_URL: str = "redis://localhost:6379"
    
    CELERY_BROKER_URL: str = "redis://localhost:6379/0"
    CELERY_RESULT_BACKEND: str = "redis://localhost:6379/0"
    
    SECRET_KEY: str = "your-secret-key-here-change-in-production"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    CORS_ORIGINS: List[str] = ["http://localhost:3000", "http://localhost:8000"]
    
    # SSH Configuration
    SSH_TIMEOUT: int = 300  # 5 minutes
    SSH_RETRY_COUNT: int = 3
    SSH_DEFAULT_PORT: int = 22
    
    MAX_CONCURRENT_SSH_CONNECTIONS: int = 10
    MAX_CONCURRENT_TESTS_PER_SETUP: int = 2
    
    class Config:
        env_file = ".env"
        case_sensitive = True
        extra = "ignore"  # Ignore extra fields from environment


settings = Settings()