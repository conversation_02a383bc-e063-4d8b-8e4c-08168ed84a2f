from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.api import deps
from app.models import Test
from app.schemas.test import Test as TestSchema, TestCreate, TestUpdate

router = APIRouter()


@router.get("/", response_model=List[TestSchema])
def get_tests(
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100
):
    """Get all tests."""
    tests = db.query(Test).offset(skip).limit(limit).all()
    return tests


@router.post("/", response_model=TestSchema)
def create_test(
    test: TestCreate,
    db: Session = Depends(deps.get_db)
):
    """Create a new test."""
    db_test = Test(**test.dict())
    db.add(db_test)
    db.commit()
    db.refresh(db_test)
    return db_test


@router.get("/{test_id}", response_model=TestSchema)
def get_test(
    test_id: int,
    db: Session = Depends(deps.get_db)
):
    """Get a specific test by ID."""
    test = db.query(Test).filter(Test.id == test_id).first()
    if not test:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Test {test_id} not found"
        )
    return test