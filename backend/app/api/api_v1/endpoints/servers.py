from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.api import deps
from app.models import Server
from app.schemas.server import ServerResponse as ServerSchema, ServerCreate, ServerUpdate

router = APIRouter()


@router.get("/", response_model=List[ServerSchema])
def get_servers(
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    setup_id: Optional[int] = None
):
    """Get all servers with optional filtering by setup."""
    query = db.query(Server)
    if setup_id is not None:
        query = query.filter(Server.setup_id == setup_id)
    servers = query.offset(skip).limit(limit).all()
    return servers


@router.post("/", response_model=ServerSchema)
def create_server(
    server: ServerCreate,
    db: Session = Depends(deps.get_db)
):
    """Create a new server."""
    db_server = Server(**server.dict())
    db.add(db_server)
    db.commit()
    db.refresh(db_server)
    return db_server


@router.get("/{server_id}", response_model=ServerSchema)
def get_server(
    server_id: int,
    db: Session = Depends(deps.get_db)
):
    """Get a specific server by ID."""
    server = db.query(Server).filter(Server.id == server_id).first()
    if not server:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Server {server_id} not found"
        )
    return server


@router.put("/{server_id}", response_model=ServerSchema)
def update_server(
    server_id: int,
    server_update: ServerUpdate,
    db: Session = Depends(deps.get_db)
):
    """Update a server."""
    server = db.query(Server).filter(Server.id == server_id).first()
    if not server:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Server {server_id} not found"
        )
    
    update_data = server_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(server, field, value)
    
    db.commit()
    db.refresh(server)
    return server


@router.delete("/{server_id}")
def delete_server(
    server_id: int,
    db: Session = Depends(deps.get_db)
):
    """Delete a server."""
    server = db.query(Server).filter(Server.id == server_id).first()
    if not server:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Server {server_id} not found"
        )
    
    db.delete(server)
    db.commit()
    return {"detail": f"Server {server_id} deleted"}
