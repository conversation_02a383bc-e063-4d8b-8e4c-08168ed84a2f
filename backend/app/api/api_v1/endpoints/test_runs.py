from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.api import deps
from app.models import TestRun
from app.schemas.test_run import TestRun as TestRunSchema, TestRunCreate

router = APIRouter()


@router.get("/", response_model=List[TestRunSchema])
def get_test_runs(
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100
):
    """Get all test runs."""
    test_runs = db.query(TestRun).offset(skip).limit(limit).all()
    return test_runs


@router.post("/", response_model=TestRunSchema)
def create_test_run(
    test_run: TestRunCreate,
    db: Session = Depends(deps.get_db)
):
    """Create a new test run."""
    db_test_run = TestRun(**test_run.dict())
    db.add(db_test_run)
    db.commit()
    db.refresh(db_test_run)
    return db_test_run


@router.get("/{test_run_id}", response_model=TestRunSchema)
def get_test_run(
    test_run_id: int,
    db: Session = Depends(deps.get_db)
):
    """Get a specific test run by ID."""
    test_run = db.query(TestRun).filter(TestRun.id == test_run_id).first()
    if not test_run:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Test run {test_run_id} not found"
        )
    return test_run