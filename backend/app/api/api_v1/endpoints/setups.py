from typing import List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app.api.deps import get_db
from app.models import TestSetup, SetupStatus, Agent, Server
from app.schemas.setup import SetupCreate, SetupUpdate, SetupResponse, SetupListResponse
from app.schemas.server import ServerResponse
from app.schemas.agent import AgentResponse

router = APIRouter()


@router.get("/", response_model=List[SetupListResponse])
def get_setups(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    setups = db.query(TestSetup).offset(skip).limit(limit).all()
    return setups


@router.post("/", response_model=SetupResponse)
def create_setup(
    setup: SetupCreate,
    db: Session = Depends(get_db)
):
    db_setup = TestSetup(**setup.dict())
    db.add(db_setup)
    db.commit()
    db.refresh(db_setup)
    return db_setup


@router.get("/{setup_id}", response_model=SetupResponse)
def get_setup(
    setup_id: int,
    db: Session = Depends(get_db)
):
    setup = db.query(TestSetup).filter(TestSetup.id == setup_id).first()
    if not setup:
        raise HTTPException(status_code=404, detail="Setup not found")
    return setup


@router.put("/{setup_id}", response_model=SetupResponse)
def update_setup(
    setup_id: int,
    setup_update: SetupUpdate,
    db: Session = Depends(get_db)
):
    setup = db.query(TestSetup).filter(TestSetup.id == setup_id).first()
    if not setup:
        raise HTTPException(status_code=404, detail="Setup not found")
        
    for field, value in setup_update.dict(exclude_unset=True).items():
        setattr(setup, field, value)
        
    db.commit()
    db.refresh(setup)
    return setup


@router.delete("/{setup_id}")
def delete_setup(
    setup_id: int,
    db: Session = Depends(get_db)
):
    setup = db.query(TestSetup).filter(TestSetup.id == setup_id).first()
    if not setup:
        raise HTTPException(status_code=404, detail="Setup not found")
        
    db.delete(setup)
    db.commit()
    return {"message": "Setup deleted successfully"}


@router.post("/{setup_id}/start")
def start_setup(
    setup_id: int,
    db: Session = Depends(get_db)
):
    setup = db.query(TestSetup).filter(TestSetup.id == setup_id).first()
    if not setup:
        raise HTTPException(status_code=404, detail="Setup not found")
        
    if setup.status == SetupStatus.RUNNING:
        raise HTTPException(status_code=400, detail="Setup is already running")
        
    setup.status = SetupStatus.RUNNING
    db.commit()
    
    return {"message": "Setup started", "status": setup.status}


@router.post("/{setup_id}/stop")
def stop_setup(
    setup_id: int,
    db: Session = Depends(get_db)
):
    setup = db.query(TestSetup).filter(TestSetup.id == setup_id).first()
    if not setup:
        raise HTTPException(status_code=404, detail="Setup not found")

    setup.status = SetupStatus.PAUSED
    db.commit()

    return {"message": "Setup stopped", "status": setup.status}


@router.post("/{setup_id}/servers", response_model=List[ServerResponse])
def assign_servers_to_setup(
    setup_id: int,
    servers: List[dict],
    db: Session = Depends(get_db)
):
    """Assign servers to a setup."""
    setup = db.query(TestSetup).filter(TestSetup.id == setup_id).first()
    if not setup:
        raise HTTPException(status_code=404, detail="Setup not found")

    created_servers = []
    for server_data in servers:
        server_data['setup_id'] = setup_id
        db_server = Server(**server_data)
        db.add(db_server)
        created_servers.append(db_server)

    db.commit()
    for server in created_servers:
        db.refresh(server)

    return created_servers


@router.post("/{setup_id}/agents", response_model=List[AgentResponse])
def assign_agents_to_setup(
    setup_id: int,
    agents: List[dict],
    db: Session = Depends(get_db)
):
    """Assign agents to a setup."""
    setup = db.query(TestSetup).filter(TestSetup.id == setup_id).first()
    if not setup:
        raise HTTPException(status_code=404, detail="Setup not found")

    created_agents = []
    for agent_data in agents:
        agent_data['setup_id'] = setup_id
        db_agent = Agent(**agent_data)
        db.add(db_agent)
        created_agents.append(db_agent)

    db.commit()
    for agent in created_agents:
        db.refresh(agent)

    return created_agents