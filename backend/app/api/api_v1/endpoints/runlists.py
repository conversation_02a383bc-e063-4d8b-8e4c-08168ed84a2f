from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.api import deps
from app.models import Runlist, RunlistStatus, TestSetup
from app.schemas.runlist import (
    RunlistCreate, RunlistUpdate, RunlistInDB, RunlistSummary,
    RunlistExecute, RunlistTemplate, RunlistTemplateList
)
from app.tasks.scraper_tasks import execute_test_runlist

router = APIRouter()


# Predefined runlist templates
RUNLIST_TEMPLATES = [
    {
        "name": "Basic Connectivity Tests",
        "description": "Essential network connectivity and basic functionality tests",
        "category": "basic",
        "content": {
            "tests": [
                {"name": "ping_test", "enabled": True},
                {"name": "dns_resolution", "enabled": True},
                {"name": "basic_throughput", "enabled": True}
            ],
            "parameters": {
                "timeout": 300,
                "retry_count": 3
            }
        }
    },
    {
        "name": "Performance Test Suite",
        "description": "Comprehensive performance testing including throughput and latency",
        "category": "performance",
        "content": {
            "tests": [
                {"name": "throughput_1g", "enabled": True},
                {"name": "throughput_10g", "enabled": True},
                {"name": "latency_test", "enabled": True},
                {"name": "packet_loss_test", "enabled": True}
            ],
            "parameters": {
                "duration": 600,
                "packet_sizes": [64, 128, 256, 512, 1024, 1500]
            }
        }
    },
    {
        "name": "VMware NIC Certification",
        "description": "Full VMware NIC device driver certification test suite",
        "category": "certification",
        "content": {
            "tests": [
                {"name": "driver_load_unload", "enabled": True},
                {"name": "vlan_functionality", "enabled": True},
                {"name": "jumbo_frames", "enabled": True},
                {"name": "tso_gso_test", "enabled": True},
                {"name": "interrupt_coalescing", "enabled": True}
            ],
            "parameters": {
                "certification_level": "full",
                "esxi_version": "8.0"
            }
        }
    }
]


@router.get("/templates", response_model=RunlistTemplateList)
def get_runlist_templates():
    """Get available runlist templates."""
    templates = [RunlistTemplate(**template) for template in RUNLIST_TEMPLATES]
    return RunlistTemplateList(templates=templates)


@router.get("/", response_model=List[RunlistInDB])
def get_runlists(
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    setup_id: Optional[int] = None,
    is_template: Optional[bool] = None
):
    """Get all runlists with optional filtering."""
    query = db.query(Runlist)
    
    if setup_id is not None:
        query = query.filter(Runlist.setup_id == setup_id)
    
    if is_template is not None:
        query = query.filter(Runlist.is_template == is_template)
        
    runlists = query.offset(skip).limit(limit).all()
    return runlists


@router.post("/", response_model=RunlistInDB)
def create_runlist(
    runlist: RunlistCreate,
    db: Session = Depends(deps.get_db)
):
    """Create a new runlist."""
    # Validate setup exists if provided
    if runlist.setup_id:
        setup = db.query(TestSetup).filter(TestSetup.id == runlist.setup_id).first()
        if not setup:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Setup {runlist.setup_id} not found"
            )
            
    db_runlist = Runlist(
        name=runlist.name,
        description=runlist.description,
        content=runlist.content.dict(),
        is_template=runlist.is_template,
        setup_id=runlist.setup_id,
        status=RunlistStatus.DRAFT
    )
    
    db.add(db_runlist)
    db.commit()
    db.refresh(db_runlist)
    
    return db_runlist


@router.get("/{runlist_id}", response_model=RunlistInDB)
def get_runlist(
    runlist_id: int,
    db: Session = Depends(deps.get_db)
):
    """Get a specific runlist by ID."""
    runlist = db.query(Runlist).filter(Runlist.id == runlist_id).first()
    if not runlist:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Runlist {runlist_id} not found"
        )
    return runlist


@router.put("/{runlist_id}", response_model=RunlistInDB)
def update_runlist(
    runlist_id: int,
    runlist_update: RunlistUpdate,
    db: Session = Depends(deps.get_db)
):
    """Update a runlist."""
    runlist = db.query(Runlist).filter(Runlist.id == runlist_id).first()
    if not runlist:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Runlist {runlist_id} not found"
        )
        
    # Don't allow updates to executing runlists
    if runlist.status == RunlistStatus.EXECUTING:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot update a runlist that is currently executing"
        )
        
    update_data = runlist_update.dict(exclude_unset=True)
    
    if "content" in update_data:
        update_data["content"] = update_data["content"].dict()
        
    for field, value in update_data.items():
        setattr(runlist, field, value)
        
    db.commit()
    db.refresh(runlist)
    
    return runlist


@router.delete("/{runlist_id}")
def delete_runlist(
    runlist_id: int,
    db: Session = Depends(deps.get_db)
):
    """Delete a runlist."""
    runlist = db.query(Runlist).filter(Runlist.id == runlist_id).first()
    if not runlist:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Runlist {runlist_id} not found"
        )
        
    # Don't allow deletion of executing runlists
    if runlist.status == RunlistStatus.EXECUTING:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot delete a runlist that is currently executing"
        )
        
    db.delete(runlist)
    db.commit()
    
    return {"detail": f"Runlist {runlist_id} deleted"}


@router.post("/{runlist_id}/execute", response_model=dict)
def execute_runlist(
    runlist_id: int,
    execute_params: RunlistExecute,
    db: Session = Depends(deps.get_db)
):
    """Execute a runlist on its associated setup."""
    runlist = db.query(Runlist).filter(Runlist.id == runlist_id).first()
    if not runlist:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Runlist {runlist_id} not found"
        )
        
    if not runlist.setup_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Runlist must be associated with a setup to execute"
        )
        
    if runlist.status == RunlistStatus.EXECUTING:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Runlist is already executing"
        )
        
    # Update runlist content if parameters are overridden
    execution_content = runlist.content.copy()
    if execute_params.parameters_override:
        execution_content["parameters"].update(execute_params.parameters_override)
        
    # Filter tests if specific ones are requested
    if execute_params.test_names:
        execution_content["tests"] = [
            test for test in execution_content["tests"]
            if test["name"] in execute_params.test_names
        ]
        
    # Update runlist status
    runlist.status = RunlistStatus.PENDING
    db.commit()
    
    # Create a test run and dispatch to Celery
    from app.models import TestRun, TestStatus
    test_run = TestRun(
        setup_id=runlist.setup_id,
        name=f"Runlist: {runlist.name}",
        status=TestStatus.PENDING
    )
    db.add(test_run)
    db.commit()
    
    # Associate runlist with test run
    runlist.test_run_id = test_run.id
    db.commit()
    
    # Execute via Celery
    task = execute_test_runlist.delay(test_run.id, execution_content)
    
    return {
        "runlist_id": runlist_id,
        "test_run_id": test_run.id,
        "task_id": task.id,
        "status": "dispatched"
    }


@router.get("/{runlist_id}/summary", response_model=RunlistSummary)
def get_runlist_summary(
    runlist_id: int,
    db: Session = Depends(deps.get_db)
):
    """Get execution summary for a runlist."""
    runlist = db.query(Runlist).filter(Runlist.id == runlist_id).first()
    if not runlist:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Runlist {runlist_id} not found"
        )
        
    summary = runlist.get_summary()
    
    # Calculate progress
    total = summary["total"]
    completed = summary["passed"] + summary["failed"] + summary["skipped"] + summary["error"]
    progress = (completed / total * 100) if total > 0 else 0
    
    return RunlistSummary(
        id=runlist.id,
        name=runlist.name,
        status=runlist.status,
        total_tests=total,
        passed_tests=summary["passed"],
        failed_tests=summary["failed"],
        skipped_tests=summary["skipped"],
        error_tests=summary["error"],
        progress_percentage=progress
    )


@router.post("/from-template", response_model=RunlistInDB)
def create_runlist_from_template(
    template_name: str,
    setup_id: int,
    db: Session = Depends(deps.get_db)
):
    """Create a new runlist from a template."""
    # Find template
    template = next((t for t in RUNLIST_TEMPLATES if t["name"] == template_name), None)
    if not template:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Template '{template_name}' not found"
        )
        
    # Validate setup exists
    setup = db.query(TestSetup).filter(TestSetup.id == setup_id).first()
    if not setup:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Setup {setup_id} not found"
        )
        
    # Create runlist from template
    db_runlist = Runlist(
        name=f"{template['name']} - {setup.name}",
        description=template["description"],
        content=template["content"],
        is_template=False,
        setup_id=setup_id,
        status=RunlistStatus.DRAFT
    )
    
    db.add(db_runlist)
    db.commit()
    db.refresh(db_runlist)
    
    return db_runlist