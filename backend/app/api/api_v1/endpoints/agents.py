from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.api import deps
from app.models import Agent
from app.schemas.agent import AgentResponse as AgentSchema, AgentCreate, AgentUpdate
from app.tasks.scraper_tasks import check_agent_status
from app.scrapers import VMwareAgentScraper
import asyncio

router = APIRouter()


@router.get("/", response_model=List[AgentSchema])
def get_agents(
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    setup_id: Optional[int] = None
):
    """Get all agents with optional filtering by setup."""
    query = db.query(Agent)
    if setup_id is not None:
        query = query.filter(Agent.setup_id == setup_id)
    agents = query.offset(skip).limit(limit).all()
    return agents


@router.post("/", response_model=AgentSchema)
def create_agent(
    agent: AgentCreate,
    db: Session = Depends(deps.get_db)
):
    """Create a new agent."""
    db_agent = Agent(**agent.dict())
    db.add(db_agent)
    db.commit()
    db.refresh(db_agent)
    return db_agent


@router.get("/{agent_id}", response_model=AgentSchema)
def get_agent(
    agent_id: int,
    db: Session = Depends(deps.get_db)
):
    """Get a specific agent by ID."""
    agent = db.query(Agent).filter(Agent.id == agent_id).first()
    if not agent:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Agent {agent_id} not found"
        )
    return agent


@router.put("/{agent_id}", response_model=AgentSchema)
def update_agent(
    agent_id: int,
    agent_update: AgentUpdate,
    db: Session = Depends(deps.get_db)
):
    """Update an agent."""
    agent = db.query(Agent).filter(Agent.id == agent_id).first()
    if not agent:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Agent {agent_id} not found"
        )
    
    update_data = agent_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(agent, field, value)
    
    db.commit()
    db.refresh(agent)
    return agent


@router.delete("/{agent_id}")
def delete_agent(
    agent_id: int,
    db: Session = Depends(deps.get_db)
):
    """Delete an agent."""
    agent = db.query(Agent).filter(Agent.id == agent_id).first()
    if not agent:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Agent {agent_id} not found"
        )
    
    db.delete(agent)
    db.commit()
    return {"detail": f"Agent {agent_id} deleted"}


@router.post("/{agent_id}/check-status")
def check_agent_health(
    agent_id: int,
    db: Session = Depends(deps.get_db)
):
    """Check agent health status via SSH."""
    agent = db.query(Agent).filter(Agent.id == agent_id).first()
    if not agent:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Agent {agent_id} not found"
        )
    
    # Dispatch async task
    task = check_agent_status.delay(agent_id)
    
    return {
        "task_id": task.id,
        "status": "checking",
        "agent_id": agent_id
    }


@router.get("/{agent_id}/available-tests")
async def get_available_tests(
    agent_id: int,
    db: Session = Depends(deps.get_db)
):
    """Get available tests from the agent's VIVa runlist."""
    agent = db.query(Agent).filter(Agent.id == agent_id).first()
    if not agent:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Agent {agent_id} not found"
        )
    
    try:
        async with VMwareAgentScraper(
            agent_ip=agent.ip_address,
            agent_port=agent.port or 22,
            username=agent.username or "root",
            password=agent.password or "vmware"
        ) as scraper:
            tests = await scraper.list_available_tests()
            categories = await scraper.get_test_categories()
            parsed = await scraper.parse_viva_runlist()
            
            return {
                "agent_id": agent_id,
                "total_tests": len(tests),
                "tests": tests,
                "categories": categories,
                "parameter_groups": parsed['parameter_groups'],
                "environment": parsed['environment']
            }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get tests from agent: {str(e)}"
        )


@router.get("/{agent_id}/runlist")
async def get_agent_runlist(
    agent_id: int,
    db: Session = Depends(deps.get_db)
):
    """Get the current runlist from the agent."""
    agent = db.query(Agent).filter(Agent.id == agent_id).first()
    if not agent:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Agent {agent_id} not found"
        )
    
    try:
        async with VMwareAgentScraper(
            agent_ip=agent.ip_address,
            agent_port=agent.port or 22,
            username=agent.username or "root",
            password=agent.password or "vmware"
        ) as scraper:
            runlist = await scraper.ssh_executor.get_runlist_results_async()
            
            return {
                "agent_id": agent_id,
                "runlist": runlist
            }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get runlist from agent: {str(e)}"
        )