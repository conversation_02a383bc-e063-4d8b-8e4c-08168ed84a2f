from fastapi import APIRouter

from app.api.api_v1.endpoints import setups, agents, servers, tests, test_runs, websocket, runlists

api_router = APIRouter()

api_router.include_router(setups.router, prefix="/setups", tags=["setups"])
api_router.include_router(agents.router, prefix="/agents", tags=["agents"])
api_router.include_router(servers.router, prefix="/servers", tags=["servers"])
api_router.include_router(tests.router, prefix="/tests", tags=["tests"])
api_router.include_router(test_runs.router, prefix="/test-runs", tags=["test-runs"])
api_router.include_router(runlists.router, prefix="/runlists", tags=["runlists"])
api_router.include_router(websocket.router, prefix="/ws", tags=["websocket"])