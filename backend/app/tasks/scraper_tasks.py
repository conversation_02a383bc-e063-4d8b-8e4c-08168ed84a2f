from celery import Task
from typing import Dict, Any, List, Optional
import asyncio
import logging
from datetime import datetime
from sqlalchemy.orm import Session
import json

from app.core.celery_app import celery_app
from app.db.base import SessionLocal
from app.models import Agent, TestExecution, TestStatus, Test, TestRun
from app.scrapers import VMwareAgentScraper

logger = logging.getLogger(__name__)


class CallbackTask(Task):
    def on_success(self, retval, task_id, args, kwargs):
        logger.info(f"Task {task_id} succeeded with result: {retval}")
        
    def on_failure(self, exc, task_id, args, kwargs, einfo):
        logger.error(f"Task {task_id} failed with exception: {exc}")


@celery_app.task(bind=True, base=CallbackTask, name="check_agent_status")
def check_agent_status(self, agent_id: int) -> Dict[str, Any]:
    """Check agent status via SSH connection."""
    db = SessionLocal()
    try:
        agent = db.query(Agent).filter(Agent.id == agent_id).first()
        if not agent:
            return {"error": f"Agent {agent_id} not found"}
            
        async def _check_status():
            async with VMwareAgentScraper(
                agent_ip=agent.ip_address,
                agent_port=22,  # SSH port
                username=agent.username or "root",
                password=agent.password or "vmware"
            ) as scraper:
                return await scraper.get_agent_status()
                
        status = asyncio.run(_check_status())
        
        agent.last_seen = datetime.utcnow()
        agent.is_active = status.get('online', False)
        db.commit()
        
        return status
        
    except Exception as e:
        logger.error(f"Error checking agent status: {e}")
        return {"error": str(e), "online": False}
    finally:
        db.close()


@celery_app.task(bind=True, base=CallbackTask, name="execute_test_runlist")
def execute_test_runlist(self, test_run_id: int, runlist_data: Dict[str, Any]) -> Dict[str, Any]:
    """Execute tests using runlist approach."""
    db = SessionLocal()
    try:
        test_run = db.query(TestRun).filter(TestRun.id == test_run_id).first()
        if not test_run:
            return {"error": f"Test run {test_run_id} not found"}
            
        # Get primary agent for the setup
        primary_agent = next((a for a in test_run.setup.agents if a.is_primary), None)
        if not primary_agent:
            return {"error": "No primary agent found for setup"}
            
        # Update test run status
        test_run.status = TestStatus.RUNNING
        test_run.started_at = datetime.utcnow()
        db.commit()
        
        async def _run_tests():
            async with VMwareAgentScraper(
                agent_ip=primary_agent.ip_address,
                agent_port=22,
                username=primary_agent.username or "root",
                password=primary_agent.password or "vmware"
            ) as scraper:
                # Extract test names from runlist data (but don't upload it)
                test_names = [test['name'] for test in runlist_data.get('tests', [])]
                
                # Execute tests
                output = await scraper.execute_tests(test_names)
                logger.info(f"Test execution started: {output}")
                
                # Monitor test progress
                while True:
                    await asyncio.sleep(10)  # Check every 10 seconds
                    
                    status = await scraper.get_test_status()
                    if not status.get('is_running'):
                        # Tests completed, get results
                        results = await scraper.get_test_results()
                        return results
                        
        results = asyncio.run(_run_tests())
        
        # Process results and update test executions
        for test_result in results.get('tests', []):
            test_name = test_result.get('name')
            test = db.query(Test).filter(Test.name == test_name).first()
            
            if test:
                # Find or create test execution
                execution = db.query(TestExecution).filter(
                    TestExecution.test_run_id == test_run_id,
                    TestExecution.test_id == test.id
                ).first()
                
                if not execution:
                    execution = TestExecution(
                        test_id=test.id,
                        test_run_id=test_run_id,
                        agent_id=primary_agent.id,
                        status=TestStatus.PENDING
                    )
                    db.add(execution)
                    
                # Update execution with results
                execution.status = test_result.get('status', TestStatus.ERROR)
                execution.started_at = test_result.get('start_time')
                execution.completed_at = test_result.get('end_time')
                execution.duration_seconds = test_result.get('duration')
                execution.log_output = test_result.get('output', '')
                execution.error_message = test_result.get('error')
                execution.result_data = test_result.get('result', {})
                
        # Update test run summary
        test_run.completed_at = datetime.utcnow()
        db.commit()
        
        # Update progress
        update_test_run_progress.delay(test_run_id)
        
        return {
            "test_run_id": test_run_id,
            "status": "completed",
            "results": results
        }
        
    except Exception as e:
        logger.error(f"Error executing test runlist: {e}")
        if test_run:
            test_run.status = TestStatus.ERROR
            test_run.completed_at = datetime.utcnow()
            db.commit()
        return {"error": str(e)}
    finally:
        db.close()


@celery_app.task(bind=True, base=CallbackTask, name="execute_test")
def execute_test(self, execution_id: int) -> Dict[str, Any]:
    """Execute a single test (legacy method, kept for compatibility)."""
    db = SessionLocal()
    try:
        execution = db.query(TestExecution).filter(TestExecution.id == execution_id).first()
        if not execution:
            return {"error": f"Test execution {execution_id} not found"}
            
        agent = execution.agent
        test = execution.test
        
        # Create a runlist for this single test
        runlist_data = {
            "tests": [{
                "name": test.name,
                "enabled": True
            }],
            "parameters": {}  # Add default parameters if needed
        }
        
        execution.status = TestStatus.RUNNING
        execution.started_at = datetime.utcnow()
        db.commit()
        
        async def _run_test():
            async with VMwareAgentScraper(
                agent_ip=agent.ip_address,
                agent_port=22,
                username=agent.username or "root",
                password=agent.password or "vmware"
            ) as scraper:
                # Upload runlist
                await scraper.upload_runlist(runlist_data)
                
                # Execute test
                output = await scraper.execute_tests([test.name])
                
                # Wait for completion
                while True:
                    await asyncio.sleep(5)
                    status = await scraper.get_test_status()
                    
                    if not status.get('is_running'):
                        results = await scraper.get_test_results()
                        # Find this test's result
                        for test_result in results.get('tests', []):
                            if test_result.get('name') == test.name:
                                return test_result
                        return {"error": "Test result not found"}
                        
        result = asyncio.run(_run_test())
        
        execution.status = result.get('status', TestStatus.ERROR)
        execution.completed_at = datetime.utcnow()
        execution.duration_seconds = result.get('duration')
        execution.log_output = result.get('output', '')
        execution.error_message = result.get('error')
        execution.result_data = result.get('result', {})
        
        db.commit()
        
        update_test_run_progress.delay(execution.test_run_id)
        
        return {
            "execution_id": execution_id,
            "status": execution.status,
            "duration": execution.duration_seconds
        }
        
    except Exception as e:
        logger.error(f"Error executing test: {e}")
        if execution:
            execution.status = TestStatus.ERROR
            execution.error_message = str(e)
            execution.completed_at = datetime.utcnow()
            db.commit()
        return {"error": str(e)}
    finally:
        db.close()


@celery_app.task(bind=True, base=CallbackTask, name="update_test_run_progress")
def update_test_run_progress(self, test_run_id: int) -> Dict[str, Any]:
    """Update test run progress based on execution statuses."""
    db = SessionLocal()
    try:
        test_run = db.query(TestRun).filter(TestRun.id == test_run_id).first()
        if not test_run:
            return {"error": f"Test run {test_run_id} not found"}
            
        executions = test_run.test_executions
        total = len(executions)
        
        if total == 0:
            return {"progress": 0}
            
        completed = sum(1 for e in executions if e.status in [
            TestStatus.PASSED, TestStatus.FAILED, TestStatus.SKIPPED, TestStatus.ERROR, TestStatus.INTERRUPTED
        ])
        passed = sum(1 for e in executions if e.status == TestStatus.PASSED)
        failed = sum(1 for e in executions if e.status == TestStatus.FAILED)
        skipped = sum(1 for e in executions if e.status == TestStatus.SKIPPED)
        interrupted = sum(1 for e in executions if e.status == TestStatus.INTERRUPTED)
        
        test_run.total_tests = total
        test_run.passed_tests = passed
        test_run.failed_tests = failed
        test_run.skipped_tests = skipped
        test_run.interrupted_tests = interrupted
        test_run.progress_percentage = (completed / total) * 100 if total > 0 else 0
        
        if completed == total:
            test_run.status = TestStatus.PASSED if failed == 0 else TestStatus.FAILED
            test_run.completed_at = datetime.utcnow()
        elif any(e.status == TestStatus.RUNNING for e in executions):
            test_run.status = TestStatus.RUNNING
            
        db.commit()
        
        return {
            "test_run_id": test_run_id,
            "progress": test_run.progress_percentage,
            "status": test_run.status,
            "total": total,
            "completed": completed,
            "passed": passed,
            "failed": failed,
            "skipped": skipped
        }
        
    except Exception as e:
        logger.error(f"Error updating test run progress: {e}")
        return {"error": str(e)}
    finally:
        db.close()


@celery_app.task(bind=True, base=CallbackTask, name="dispatch_tests_with_runlist")
def dispatch_tests_with_runlist(self, test_run_id: int, test_ids: List[int], 
                                runlist_template: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """Dispatch tests using runlist approach."""
    db = SessionLocal()
    try:
        test_run = db.query(TestRun).filter(TestRun.id == test_run_id).first()
        if not test_run:
            return {"error": f"Test run {test_run_id} not found"}
            
        # Get tests
        tests = db.query(Test).filter(Test.id.in_(test_ids)).all()
        if not tests:
            return {"error": "No tests found"}
            
        # Create runlist
        runlist_data = runlist_template or {"parameters": {}}
        runlist_data['tests'] = []
        
        for test in tests:
            runlist_data['tests'].append({
                'name': test.name,
                'enabled': True,
                'type': test.test_type
            })
            
        # Create test executions for tracking
        for test in tests:
            execution = TestExecution(
                test_id=test.id,
                test_run_id=test_run_id,
                agent_id=test_run.setup.agents[0].id,  # Primary agent
                status=TestStatus.PENDING
            )
            db.add(execution)
            
        db.commit()
        
        # Execute runlist
        execute_test_runlist.delay(test_run_id, runlist_data)
        
        return {
            "test_run_id": test_run_id,
            "tests_count": len(tests),
            "runlist_created": True
        }
        
    except Exception as e:
        logger.error(f"Error dispatching tests with runlist: {e}")
        return {"error": str(e)}
    finally:
        db.close()


@celery_app.task(bind=True, base=CallbackTask, name="dispatch_tests")
def dispatch_tests(self, test_run_id: int, test_ids: List[int]) -> Dict[str, Any]:
    """Legacy dispatch method - redirects to runlist approach."""
    return dispatch_tests_with_runlist(test_run_id, test_ids)