#!/usr/bin/env python3
"""Create test data in the database."""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.db.base import SessionLocal, engine, Base
from app.models import TestSetup, Agent, Test, TestType
from sqlalchemy.orm import Session

# Create tables
Base.metadata.create_all(bind=engine)

def create_test_data():
    db = SessionLocal()
    
    try:
        # Check if data already exists
        existing_setup = db.query(TestSetup).first()
        if existing_setup:
            print("Test data already exists. Skipping creation.")
            return
            
        # Create a test setup
        setup = TestSetup(
            name="VIVa Test Lab - Shelf 65",
            description="VMware NIC certification test environment"
        )
        db.add(setup)
        db.commit()
        
        # Create agents
        primary_agent = Agent(
            name="shelf65sut",
            ip_address="**************",
            port=22,
            username="root",
            password="vmware",
            is_primary=True,
            is_active=True,
            setup_id=setup.id
        )
        db.add(primary_agent)
        
        secondary_agent = Agent(
            name="shelf65aux",
            ip_address="**************",
            port=22,
            username="root",
            password="vmware",
            is_primary=False,
            is_active=True,
            setup_id=setup.id
        )
        db.add(secondary_agent)
        
        # Create some sample tests
        sample_tests = [
            {"name": "Networking::Functional::NoVM_IPv4Ping", "test_type": TestType.SINGLE_HOST, "category": "Networking"},
            {"name": "Networking::Functional::NoVM_HardwareCheck", "test_type": TestType.SINGLE_HOST, "category": "Networking"},
            {"name": "DDV::Functional::EIRandomNoReload", "test_type": TestType.SINGLE_HOST, "category": "DDV"},
            {"name": "Networking::Functional::VMOTION", "test_type": TestType.DUAL_HOST, "category": "Networking"},
            {"name": "Asyncdriver::Functional::Api_complaint", "test_type": TestType.SINGLE_HOST, "category": "Asyncdriver"},
        ]
        
        for test_data in sample_tests:
            test = Test(
                name=test_data["name"],
                description=f"Test for {test_data['name']}",
                test_type=test_data["test_type"],
                category=test_data["category"],
                estimated_duration=300,  # 5 minutes
                priority=5
            )
            db.add(test)
        
        db.commit()
        
        print("✅ Test data created successfully!")
        print(f"   Setup: {setup.name}")
        print(f"   Primary Agent: {primary_agent.name} ({primary_agent.ip_address})")
        print(f"   Secondary Agent: {secondary_agent.name} ({secondary_agent.ip_address})")
        print(f"   Tests: {len(sample_tests)} sample tests")
        
    except Exception as e:
        print(f"❌ Error creating test data: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    create_test_data()