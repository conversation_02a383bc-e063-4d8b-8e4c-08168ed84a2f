#!/bin/bash

# Stop development services script

echo "Stopping development services..."

# Stop all related processes
echo "Stopping FastAPI backend..."
pkill -f "uvicorn.*app.main:app"

echo "Stopping Celery worker..."
pkill -f "celery.*worker"

echo "Stopping React frontend..."
pkill -f "vite"

echo "Stopping Redis (if started by script)..."
pkill -f "redis-server"

echo "All development services stopped."
