import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Typography,
  Box,
  LinearProgress,
} from '@mui/material';
import { CheckCircle, Cancel, Error, Schedule, Block } from '@mui/icons-material';

interface TestResult {
  name: string;
  status: string;
  duration?: number;
  start_time?: string;
  end_time?: string;
  error?: string;
}

interface TestResultsTableProps {
  results: TestResult[];
  title?: string;
}

const getStatusIcon = (status: string) => {
  switch (status.toLowerCase()) {
    case 'passed':
    case 'success':
      return <CheckCircle fontSize="small" />;
    case 'failed':
    case 'failure':
      return <Cancel fontSize="small" />;
    case 'running':
    case 'in_progress':
      return <Schedule fontSize="small" />;
    case 'skipped':
      return <Block fontSize="small" />;
    default:
      return <Error fontSize="small" />;
  }
};

const getStatusColor = (status: string): 'success' | 'error' | 'warning' | 'info' | 'default' => {
  switch (status.toLowerCase()) {
    case 'passed':
    case 'success':
      return 'success';
    case 'failed':
    case 'failure':
      return 'error';
    case 'running':
    case 'in_progress':
      return 'info';
    case 'skipped':
      return 'warning';
    default:
      return 'default';
  }
};

const formatDuration = (seconds?: number): string => {
  if (!seconds) return '-';
  
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  
  if (hours > 0) {
    return `${hours}h ${minutes}m ${secs}s`;
  } else if (minutes > 0) {
    return `${minutes}m ${secs}s`;
  } else {
    return `${secs}s`;
  }
};

export default function TestResultsTable({ results, title }: TestResultsTableProps) {
  const totalTests = results.length;
  const passedTests = results.filter(r => r.status.toLowerCase() === 'passed').length;
  const failedTests = results.filter(r => r.status.toLowerCase() === 'failed').length;
  const runningTests = results.filter(r => r.status.toLowerCase() === 'running').length;
  
  const passRate = totalTests > 0 ? (passedTests / totalTests) * 100 : 0;

  return (
    <Box>
      {title && (
        <Typography variant="h6" gutterBottom>
          {title}
        </Typography>
      )}
      
      <Box sx={{ mb: 2 }}>
        <Box sx={{ display: 'flex', gap: 2, mb: 1 }}>
          <Chip
            icon={<CheckCircle />}
            label={`Passed: ${passedTests}`}
            color="success"
            size="small"
          />
          <Chip
            icon={<Cancel />}
            label={`Failed: ${failedTests}`}
            color="error"
            size="small"
          />
          {runningTests > 0 && (
            <Chip
              icon={<Schedule />}
              label={`Running: ${runningTests}`}
              color="info"
              size="small"
            />
          )}
          <Chip
            label={`Total: ${totalTests}`}
            size="small"
          />
        </Box>
        
        {totalTests > 0 && (
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Box sx={{ width: '100%', mr: 1 }}>
              <LinearProgress
                variant="determinate"
                value={passRate}
                color={passRate === 100 ? 'success' : failedTests > 0 ? 'error' : 'primary'}
                sx={{ height: 8, borderRadius: 4 }}
              />
            </Box>
            <Typography variant="body2" color="text.secondary">
              {passRate.toFixed(1)}%
            </Typography>
          </Box>
        )}
      </Box>

      <TableContainer component={Paper}>
        <Table size="small">
          <TableHead>
            <TableRow>
              <TableCell>Test Name</TableCell>
              <TableCell width={120}>Status</TableCell>
              <TableCell width={100}>Duration</TableCell>
              <TableCell width={150}>Start Time</TableCell>
              <TableCell width={150}>End Time</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {results.map((result, index) => (
              <TableRow key={index}>
                <TableCell>
                  <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                    {result.name}
                  </Typography>
                  {result.error && (
                    <Typography variant="caption" color="error" display="block">
                      Error: {result.error}
                    </Typography>
                  )}
                </TableCell>
                <TableCell>
                  <Chip
                    icon={getStatusIcon(result.status)}
                    label={result.status}
                    color={getStatusColor(result.status)}
                    size="small"
                  />
                </TableCell>
                <TableCell>{formatDuration(result.duration)}</TableCell>
                <TableCell>
                  {result.start_time 
                    ? new Date(result.start_time).toLocaleString() 
                    : '-'}
                </TableCell>
                <TableCell>
                  {result.end_time 
                    ? new Date(result.end_time).toLocaleString() 
                    : '-'}
                </TableCell>
              </TableRow>
            ))}
            {results.length === 0 && (
              <TableRow>
                <TableCell colSpan={5} align="center">
                  <Typography color="text.secondary">
                    No test results available
                  </Typography>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
}