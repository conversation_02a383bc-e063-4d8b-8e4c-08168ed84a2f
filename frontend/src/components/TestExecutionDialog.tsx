import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON><PERSON><PERSON>,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Box,
  Typography,
  Alert,
  AlertTitle,
  Stack,
  Divider,
} from '@mui/material';

interface TestExecutionDialogProps {
  open: boolean;
  onClose: () => void;
  selectedTests: string[];
  setupId?: string;
  onExecute: (config: ExecutionConfig) => void;
}

interface ExecutionConfig {
  name: string;
  description?: string;
  setupId: string;
  tests: string[];
  parameters?: Record<string, any>;
}

export default function TestExecutionDialog({
  open,
  onClose,
  selectedTests,
  setupId,
  onExecute,
}: TestExecutionDialogProps) {
  const [runName, setRunName] = useState(`Test Run - ${new Date().toLocaleString()}`);
  const [description, setDescription] = useState('');
  const [error, setError] = useState<string | null>(null);

  const handleExecute = () => {
    if (!runName.trim()) {
      setError('Please provide a run name');
      return;
    }

    if (!setupId) {
      setError('No setup selected');
      return;
    }

    const config: ExecutionConfig = {
      name: runName,
      description: description || undefined,
      setupId,
      tests: selectedTests,
      parameters: {
        // Add any default parameters here
      },
    };

    onExecute(config);
    handleClose();
  };

  const handleClose = () => {
    setRunName(`Test Run - ${new Date().toLocaleString()}`);
    setDescription('');
    setError(null);
    onClose();
  };

  // Group tests by category
  const testsByCategory = selectedTests.reduce((acc, test) => {
    const category = test.split('::')[0];
    if (!acc[category]) acc[category] = [];
    acc[category].push(test);
    return acc;
  }, {} as Record<string, string[]>);

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
      <DialogTitle>Execute Selected Tests</DialogTitle>
      <DialogContent>
        <Stack spacing={3} sx={{ mt: 1 }}>
          {error && (
            <Alert severity="error" onClose={() => setError(null)}>
              {error}
            </Alert>
          )}

          <TextField
            label="Run Name"
            fullWidth
            value={runName}
            onChange={(e) => setRunName(e.target.value)}
            required
          />

          <TextField
            label="Description"
            fullWidth
            multiline
            rows={3}
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            placeholder="Optional description for this test run..."
          />

          <Box>
            <Typography variant="subtitle2" gutterBottom>
              Selected Tests ({selectedTests.length})
            </Typography>
            <Box sx={{ maxHeight: 200, overflowY: 'auto', border: 1, borderColor: 'divider', borderRadius: 1, p: 1 }}>
              {Object.entries(testsByCategory).map(([category, tests]) => (
                <Box key={category} sx={{ mb: 1 }}>
                  <Typography variant="caption" color="text.secondary">
                    {category}
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {tests.map(test => (
                      <Chip
                        key={test}
                        label={test.split('::').slice(1).join('::')}
                        size="small"
                        variant="outlined"
                      />
                    ))}
                  </Box>
                </Box>
              ))}
            </Box>
          </Box>

          <Alert severity="warning">
            <AlertTitle>Important</AlertTitle>
            Tests will be executed on the VIVa agent using SSH. The system will use the existing
            runlist and monitor the execution progress.
            <br />
            <strong>NO recreating runlist. Only allowed modification is testing parameters.</strong>
          </Alert>
        </Stack>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose}>Cancel</Button>
        <Button onClick={handleExecute} variant="contained" color="primary">
          Execute Tests
        </Button>
      </DialogActions>
    </Dialog>
  );
}