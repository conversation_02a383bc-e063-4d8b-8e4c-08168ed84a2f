import { useParams, useNavigate } from 'react-router-dom'
import { useQuery } from '@tanstack/react-query'
import {
  Box,
  Typography,
  Paper,
  Grid,
  LinearProgress,
  Chip,
  Card,
  CardContent,
  Button,
} from '@mui/material'
import { Science as ScienceIcon, PlayArrow } from '@mui/icons-material'
import axios from 'axios'

interface Agent {
  id: number
  name: string
  ip_address: string
  is_primary: boolean
  status: string
  last_heartbeat: string
}

interface Setup {
  id: number
  name: string
  is_active: boolean
  created_at: string
  agents: Agent[]
}

export default function SetupDetail() {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  
  const { data: setup, isLoading } = useQuery<Setup>({
    queryKey: ['setup', id],
    queryFn: async () => {
      const response = await axios.get(`/api/v1/setups/${id}`)
      return response.data
    },
    enabled: !!id,
  })

  if (isLoading) {
    return <LinearProgress />
  }

  if (!setup) {
    return <Typography>Setup not found</Typography>
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3, gap: 2 }}>
        <Typography variant="h4">{setup.name}</Typography>
        <Chip
          label={setup.is_active ? 'Active' : 'Inactive'}
          color={setup.is_active ? 'success' : 'error'}
        />
        <Box sx={{ flexGrow: 1 }} />
        <Button
          variant="outlined"
          startIcon={<ScienceIcon />}
          onClick={() => navigate(`/test-catalog/${id}`)}
        >
          View Test Catalog
        </Button>
        <Button
          variant="contained"
          startIcon={<PlayArrow />}
          onClick={() => navigate(`/test-catalog/${id}`)}
        >
          Run Tests
        </Button>
      </Box>

      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Setup Information
            </Typography>
            <Typography>
              Created: {new Date(setup.created_at).toLocaleString()}
            </Typography>
          </Paper>
        </Grid>

        <Grid item xs={12}>
          <Typography variant="h6" gutterBottom>
            Agents
          </Typography>
          <Grid container spacing={2}>
            {setup.agents.map((agent) => (
              <Grid item xs={12} md={6} key={agent.id}>
                <Card>
                  <CardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                      <Typography variant="h6">{agent.name}</Typography>
                      <Chip
                        label={agent.is_primary ? 'Primary' : 'Secondary'}
                        color={agent.is_primary ? 'primary' : 'default'}
                        size="small"
                      />
                    </Box>
                    <Typography color="textSecondary">
                      IP: {agent.ip_address}
                    </Typography>
                    <Typography color="textSecondary">
                      Status: {agent.status}
                    </Typography>
                    <Typography color="textSecondary">
                      Last Heartbeat: {new Date(agent.last_heartbeat).toLocaleString()}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Grid>
      </Grid>
    </Box>
  )
}