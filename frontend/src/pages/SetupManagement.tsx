import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  TextField,
  Button,
  Card,
  CardContent,
  CardActions,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Computer as ComputerIcon,
  Storage as StorageIcon,
} from '@mui/icons-material';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import axios from 'axios';

interface Server {
  id?: number;
  name: string;
  hostname: string;
  ip_address: string;
  server_type: 'sut' | 'aux';
  username: string;
  password: string;
  setup_id?: number;
  is_active?: boolean;
}

interface Agent {
  id?: number;
  name: string;
  ip_address: string;
  port: number;
  username: string;
  password: string;
  is_primary: boolean;
  setup_id?: number;
}

interface Setup {
  id?: number;
  name: string;
  description: string;
  servers: Server[];
  agents: Agent[];
}

export default function SetupManagement() {
  const [open, setOpen] = useState(false);
  const [editingSetup, setEditingSetup] = useState<Setup | null>(null);
  const [setupForm, setSetupForm] = useState<Setup>({
    name: '',
    description: '',
    servers: [],
    agents: [],
  });

  const queryClient = useQueryClient();

  const { data: setups = [] } = useQuery<Setup[]>({
    queryKey: ['setups'],
    queryFn: async () => {
      const response = await axios.get('/api/v1/setups/');
      return response.data;
    },
  });

  const createSetupMutation = useMutation({
    mutationFn: async (setup: Setup) => {
      const response = await axios.post('/api/v1/setups/', {
        name: setup.name,
        description: setup.description,
      });
      const setupId = response.data.id;

      // Create servers
      if (setup.servers.length > 0) {
        await axios.post(`/api/v1/setups/${setupId}/servers`, setup.servers);
      }

      // Create agents
      if (setup.agents.length > 0) {
        await axios.post(`/api/v1/setups/${setupId}/agents`, setup.agents);
      }

      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['setups'] });
      setOpen(false);
      resetForm();
    },
  });

  const updateSetupMutation = useMutation({
    mutationFn: async (setup: Setup) => {
      if (!setup.id) throw new Error('Setup ID is required for update');

      // Update setup basic info
      const response = await axios.put(`/api/v1/setups/${setup.id}/`, {
        name: setup.name,
        description: setup.description,
      });

      // For now, we'll just return the response
      // TODO: Implement server and agent updates
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['setups'] });
      setOpen(false);
      resetForm();
    },
  });

  const deleteSetupMutation = useMutation({
    mutationFn: async (setupId: number) => {
      await axios.delete(`/api/v1/setups/${setupId}/`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['setups'] });
    },
  });

  const resetForm = () => {
    setSetupForm({
      name: '',
      description: '',
      servers: [],
      agents: [],
    });
    setEditingSetup(null);
  };

  const handleOpenDialog = async (setup?: Setup) => {
    if (setup) {
      setEditingSetup(setup);

      // Fetch full setup details including servers and agents
      try {
        const response = await axios.get(`/api/v1/setups/${setup.id}`);
        const fullSetup = response.data;

        // Ensure servers and agents arrays exist
        setSetupForm({
          ...fullSetup,
          servers: fullSetup.servers || [],
          agents: fullSetup.agents || [],
        });
      } catch (error) {
        console.error('Failed to fetch setup details:', error);
        // Fallback to basic setup data
        setSetupForm({
          ...setup,
          servers: [],
          agents: [],
        });
      }
    } else {
      resetForm();
    }
    setOpen(true);
  };

  const handleCloseDialog = () => {
    setOpen(false);
    resetForm();
  };

  const handleSubmit = () => {
    if (editingSetup) {
      updateSetupMutation.mutate(setupForm);
    } else {
      createSetupMutation.mutate(setupForm);
    }
  };

  const addServer = () => {
    setSetupForm({
      ...setupForm,
      servers: [
        ...setupForm.servers,
        {
          name: '',
          hostname: '',
          ip_address: '',
          server_type: 'sut',
          username: 'root',
          password: 'ca$hc0w',
        },
      ],
    });
  };

  const updateServer = (index: number, server: Server) => {
    const newServers = [...setupForm.servers];
    newServers[index] = server;
    setSetupForm({ ...setupForm, servers: newServers });
  };

  const removeServer = (index: number) => {
    const newServers = setupForm.servers.filter((_, i) => i !== index);
    setSetupForm({ ...setupForm, servers: newServers });
  };

  const addAgent = () => {
    setSetupForm({
      ...setupForm,
      agents: [
        ...setupForm.agents,
        {
          name: '',
          ip_address: '',
          port: 22,
          username: 'root',
          password: 'vmware',
          is_primary: setupForm.agents.length === 0,
        },
      ],
    });
  };

  const updateAgent = (index: number, agent: Agent) => {
    const newAgents = [...setupForm.agents];
    newAgents[index] = agent;
    setSetupForm({ ...setupForm, agents: newAgents });
  };

  const removeAgent = (index: number) => {
    const newAgents = setupForm.agents.filter((_, i) => i !== index);
    setSetupForm({ ...setupForm, agents: newAgents });
  };

  const generateShelfSetup = () => {
    const shelfNumber = prompt('Enter shelf number (e.g., 65):');
    if (!shelfNumber) return;

    setSetupForm({
      name: `Shelf${shelfNumber}`,
      description: `VMware NIC certification test environment for Shelf ${shelfNumber}`,
      servers: [
        {
          name: `shelf${shelfNumber}sut`,
          hostname: `shelf${shelfNumber}sut.esxcert.local`,
          ip_address: `172.101.${shelfNumber}.11`,
          server_type: 'sut',
          username: 'root',
          password: 'ca$hc0w',
        },
        {
          name: `shelf${shelfNumber}aux`,
          hostname: `shelf${shelfNumber}aux.esxcert.local`,
          ip_address: `172.101.${shelfNumber}.12`,
          server_type: 'aux',
          username: 'root',
          password: 'ca$hc0w',
        },
      ],
      agents: [
        {
          name: `shelf${shelfNumber}sut`,
          ip_address: '',
          port: 22,
          username: 'root',
          password: 'vmware',
          is_primary: true,
        },
        {
          name: `shelf${shelfNumber}aux`,
          ip_address: '',
          port: 22,
          username: 'root',
          password: 'vmware',
          is_primary: false,
        },
      ],
    });
    setOpen(true);
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
        <Typography variant="h4">Setup Management</Typography>
        <Box>
          <Button
            variant="outlined"
            startIcon={<ComputerIcon />}
            onClick={generateShelfSetup}
            sx={{ mr: 1 }}
          >
            Generate Shelf Setup
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => handleOpenDialog()}
          >
            Create Setup
          </Button>
        </Box>
      </Box>

      <Grid container spacing={3}>
        {setups.map((setup) => (
          <Grid item xs={12} md={6} lg={4} key={setup.id}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  {setup.name}
                </Typography>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  {setup.description}
                </Typography>
                <Box sx={{ mt: 2 }}>
                  <Chip
                    icon={<StorageIcon />}
                    label={`${setup.servers?.length || 0} Servers`}
                    size="small"
                    sx={{ mr: 1, mb: 1 }}
                  />
                  <Chip
                    icon={<ComputerIcon />}
                    label={`${setup.agents?.length || 0} Agents`}
                    size="small"
                    sx={{ mb: 1 }}
                  />
                </Box>
              </CardContent>
              <CardActions>
                <Button
                  size="small"
                  startIcon={<EditIcon />}
                  onClick={() => handleOpenDialog(setup)}
                >
                  Edit
                </Button>
                <Button
                  size="small"
                  color="error"
                  startIcon={<DeleteIcon />}
                  onClick={() => setup.id && deleteSetupMutation.mutate(setup.id)}
                >
                  Delete
                </Button>
              </CardActions>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Create/Edit Setup Dialog */}
      <Dialog open={open} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingSetup ? 'Edit Setup' : 'Create New Setup'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 1 }}>
            <TextField
              fullWidth
              label="Setup Name"
              value={setupForm.name}
              onChange={(e) => setSetupForm({ ...setupForm, name: e.target.value })}
              sx={{ mb: 2 }}
            />
            <TextField
              fullWidth
              label="Description"
              multiline
              rows={2}
              value={setupForm.description}
              onChange={(e) => setSetupForm({ ...setupForm, description: e.target.value })}
              sx={{ mb: 3 }}
            />

            {/* Servers Section */}
            <Typography variant="h6" gutterBottom>
              Servers
              <Button
                size="small"
                startIcon={<AddIcon />}
                onClick={addServer}
                sx={{ ml: 2 }}
              >
                Add Server
              </Button>
            </Typography>
            {setupForm.servers.map((server, index) => (
              <Paper key={index} sx={{ p: 2, mb: 2 }}>
                <Grid container spacing={2} alignItems="center">
                  <Grid item xs={12} sm={6} md={3}>
                    <TextField
                      fullWidth
                      label="Name"
                      value={server.name}
                      onChange={(e) => updateServer(index, { ...server, name: e.target.value })}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <TextField
                      fullWidth
                      label="Hostname"
                      value={server.hostname}
                      onChange={(e) => updateServer(index, { ...server, hostname: e.target.value })}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <TextField
                      fullWidth
                      label="IP Address"
                      value={server.ip_address}
                      onChange={(e) => updateServer(index, { ...server, ip_address: e.target.value })}
                      placeholder="*************"
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <FormControl fullWidth>
                      <InputLabel>Type</InputLabel>
                      <Select
                        value={server.server_type}
                        label="Type"
                        onChange={(e) => updateServer(index, { ...server, server_type: e.target.value as 'sut' | 'aux' })}
                      >
                        <MenuItem value="sut">SUT (System Under Test)</MenuItem>
                        <MenuItem value="aux">AUX (Auxiliary)</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={6} md={4}>
                    <TextField
                      fullWidth
                      label="Username"
                      value={server.username}
                      onChange={(e) => updateServer(index, { ...server, username: e.target.value })}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={4}>
                    <TextField
                      fullWidth
                      label="Password"
                      type="password"
                      value={server.password}
                      onChange={(e) => updateServer(index, { ...server, password: e.target.value })}
                    />
                  </Grid>
                  <Grid item xs={12} sm={12} md={4} sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                    <IconButton onClick={() => removeServer(index)} color="error">
                      <DeleteIcon />
                    </IconButton>
                  </Grid>
                </Grid>
              </Paper>
            ))}

            {/* Agents Section */}
            <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
              Agents
              <Button
                size="small"
                startIcon={<AddIcon />}
                onClick={addAgent}
                sx={{ ml: 2 }}
              >
                Add Agent
              </Button>
            </Typography>
            {setupForm.agents.map((agent, index) => (
              <Paper key={index} sx={{ p: 2, mb: 2 }}>
                <Grid container spacing={2} alignItems="center">
                  <Grid item xs={3}>
                    <TextField
                      fullWidth
                      label="Name"
                      value={agent.name}
                      onChange={(e) => updateAgent(index, { ...agent, name: e.target.value })}
                    />
                  </Grid>
                  <Grid item xs={3}>
                    <TextField
                      fullWidth
                      label="IP Address"
                      value={agent.ip_address}
                      onChange={(e) => updateAgent(index, { ...agent, ip_address: e.target.value })}
                    />
                  </Grid>
                  <Grid item xs={2}>
                    <TextField
                      fullWidth
                      label="Port"
                      type="number"
                      value={agent.port}
                      onChange={(e) => updateAgent(index, { ...agent, port: parseInt(e.target.value) })}
                    />
                  </Grid>
                  <Grid item xs={2}>
                    <TextField
                      fullWidth
                      label="Username"
                      value={agent.username}
                      onChange={(e) => updateAgent(index, { ...agent, username: e.target.value })}
                    />
                  </Grid>
                  <Grid item xs={1}>
                    <IconButton onClick={() => removeAgent(index)} color="error">
                      <DeleteIcon />
                    </IconButton>
                  </Grid>
                </Grid>
              </Paper>
            ))}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={!setupForm.name || createSetupMutation.isPending}
          >
            {editingSetup ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}
