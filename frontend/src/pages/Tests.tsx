import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import {
  Box,
  Typography,
  Paper,
  LinearProgress,
  Chip,
} from '@mui/material'
import { DataGrid, GridColDef } from '@mui/x-data-grid'
import axios from 'axios'

interface Test {
  id: number
  name: string
  description: string
  test_type: 'single_host' | 'dual_host'
  duration_minutes: number
  created_at: string
}

const columns: GridColDef[] = [
  { field: 'id', headerName: 'ID', width: 90 },
  { field: 'name', headerName: 'Test Name', width: 250 },
  { field: 'description', headerName: 'Description', width: 350 },
  {
    field: 'test_type',
    headerName: 'Type',
    width: 150,
    renderCell: (params) => (
      <Chip
        label={params.value === 'single_host' ? 'Single Host' : 'Dual Host'}
        color={params.value === 'single_host' ? 'primary' : 'secondary'}
        size="small"
      />
    ),
  },
  {
    field: 'duration_minutes',
    headerName: 'Duration',
    width: 120,
    valueFormatter: (params) => `${params.value} min`,
  },
  {
    field: 'created_at',
    headerName: 'Created',
    width: 180,
    valueFormatter: (params) => new Date(params.value).toLocaleString(),
  },
]

export default function Tests() {
  const [pageSize, setPageSize] = useState(10)
  
  const { data: tests = [], isLoading } = useQuery<Test[]>({
    queryKey: ['tests'],
    queryFn: async () => {
      const response = await axios.get('/api/v1/tests/')
      return response.data
    },
  })

  if (isLoading) {
    return <LinearProgress />
  }

  const singleHostTests = tests.filter(t => t.test_type === 'single_host').length
  const dualHostTests = tests.filter(t => t.test_type === 'dual_host').length

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Test Definitions
      </Typography>

      <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
        <Paper sx={{ p: 2 }}>
          <Typography variant="h6">{tests.length}</Typography>
          <Typography color="textSecondary">Total Tests</Typography>
        </Paper>
        <Paper sx={{ p: 2 }}>
          <Typography variant="h6">{singleHostTests}</Typography>
          <Typography color="textSecondary">Single Host Tests</Typography>
        </Paper>
        <Paper sx={{ p: 2 }}>
          <Typography variant="h6">{dualHostTests}</Typography>
          <Typography color="textSecondary">Dual Host Tests</Typography>
        </Paper>
      </Box>
      
      <Paper sx={{ height: 600, width: '100%' }}>
        <DataGrid
          rows={tests}
          columns={columns}
          pageSize={pageSize}
          onPageSizeChange={(newPageSize) => setPageSize(newPageSize)}
          rowsPerPageOptions={[5, 10, 20]}
          disableSelectionOnClick
        />
      </Paper>
    </Box>
  )
}