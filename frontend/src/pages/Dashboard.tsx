import { Grid, Paper, Typography, Box, LinearProgress } from '@mui/material'
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, <PERSON>, Tooltip } from 'recharts'
import { useQuery } from '@tanstack/react-query'
import axios from 'axios'

interface DashboardStats {
  total_setups: number
  active_setups: number
  total_tests: number
  running_tests: number
  passed_tests: number
  failed_tests: number
}

const COLORS: Record<string, string> = {
  passed: '#4caf50',
  failed: '#f44336',
  running: '#2196f3',
  pending: '#ff9800',
}

export default function Dashboard() {
  const { data: stats, isLoading } = useQuery<DashboardStats>({
    queryKey: ['dashboard-stats'],
    queryFn: async () => {
      const response = await axios.get('/api/v1/stats/dashboard/')
      return response.data
    },
    refetchInterval: 5000
  })

  if (isLoading) {
    return <LinearProgress />
  }

  const testData = [
    { name: 'Passed', value: stats?.passed_tests || 0 },
    { name: 'Failed', value: stats?.failed_tests || 0 },
    { name: 'Running', value: stats?.running_tests || 0 },
  ]

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Test Orchestration Dashboard
      </Typography>
      
      <Grid container spacing={3}>
        <Grid item xs={12} sm={6} md={3}>
          <Paper sx={{ p: 2 }}>
            <Typography color="textSecondary" gutterBottom>
              Total Setups
            </Typography>
            <Typography variant="h3">
              {stats?.total_setups || 0}
            </Typography>
            <Typography color="textSecondary">
              {stats?.active_setups || 0} active
            </Typography>
          </Paper>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Paper sx={{ p: 2 }}>
            <Typography color="textSecondary" gutterBottom>
              Total Tests
            </Typography>
            <Typography variant="h3">
              {stats?.total_tests || 0}
            </Typography>
          </Paper>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Paper sx={{ p: 2 }}>
            <Typography color="textSecondary" gutterBottom>
              Running Tests
            </Typography>
            <Typography variant="h3" color="primary">
              {stats?.running_tests || 0}
            </Typography>
          </Paper>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Paper sx={{ p: 2 }}>
            <Typography color="textSecondary" gutterBottom>
              Success Rate
            </Typography>
            <Typography variant="h3" color="success.main">
              {stats?.total_tests ? 
                Math.round((stats.passed_tests / stats.total_tests) * 100) : 0}%
            </Typography>
          </Paper>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, height: 400 }}>
            <Typography variant="h6" gutterBottom>
              Test Results Distribution
            </Typography>
            <ResponsiveContainer width="100%" height="90%">
              <PieChart>
                <Pie
                  data={testData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={120}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {testData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[entry.name.toLowerCase()]} />
                  ))}
                </Pie>
                <Tooltip />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, height: 400 }}>
            <Typography variant="h6" gutterBottom>
              Active Test Runs
            </Typography>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  )
}