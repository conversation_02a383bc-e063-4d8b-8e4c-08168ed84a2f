import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import {
  Box,
  Typography,
  Button,
  Paper,
  LinearProgress,
  Chip,
} from '@mui/material'
import { DataGrid, GridColDef } from '@mui/x-data-grid'
import PlayArrowIcon from '@mui/icons-material/PlayArrow'
import axios from 'axios'

interface TestRun {
  id: number
  name: string
  status: 'pending' | 'running' | 'completed' | 'failed'
  total_tests: number
  completed_tests: number
  passed_tests: number
  failed_tests: number
  started_at: string
  completed_at: string | null
}

const columns: GridColDef[] = [
  { field: 'id', headerName: 'ID', width: 90 },
  { field: 'name', headerName: 'Run Name', width: 250 },
  {
    field: 'status',
    headerName: 'Status',
    width: 150,
    renderCell: (params) => {
      const statusColors = {
        pending: 'warning',
        running: 'info',
        completed: 'success',
        failed: 'error',
      }
      return (
        <Chip
          label={params.value}
          color={statusColors[params.value as keyof typeof statusColors] as any}
          size="small"
        />
      )
    },
  },
  {
    field: 'progress',
    headerName: 'Progress',
    width: 200,
    renderCell: (params) => {
      const { completed_tests, total_tests } = params.row
      const percentage = total_tests > 0 ? (completed_tests / total_tests) * 100 : 0
      return (
        <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
          <Box sx={{ width: '100%', mr: 1 }}>
            <LinearProgress variant="determinate" value={percentage} />
          </Box>
          <Box sx={{ minWidth: 35 }}>
            <Typography variant="body2" color="text.secondary">
              {`${Math.round(percentage)}%`}
            </Typography>
          </Box>
        </Box>
      )
    },
  },
  {
    field: 'passed_tests',
    headerName: 'Passed',
    width: 100,
    renderCell: (params) => (
      <Typography color="success.main">{params.value}</Typography>
    ),
  },
  {
    field: 'failed_tests',
    headerName: 'Failed',
    width: 100,
    renderCell: (params) => (
      <Typography color="error.main">{params.value}</Typography>
    ),
  },
  {
    field: 'started_at',
    headerName: 'Started',
    width: 180,
    valueFormatter: (params) => new Date(params.value).toLocaleString(),
  },
]

export default function TestRuns() {
  const [pageSize, setPageSize] = useState(10)
  
  const { data: testRuns = [], isLoading } = useQuery<TestRun[]>({
    queryKey: ['test-runs'],
    queryFn: async () => {
      const response = await axios.get('/api/v1/test-runs/')
      return response.data
    },
    refetchInterval: 5000, // Refresh every 5 seconds
  })

  if (isLoading) {
    return <LinearProgress />
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
        <Typography variant="h4">Test Runs</Typography>
        <Button
          variant="contained"
          startIcon={<PlayArrowIcon />}
          onClick={() => {
            // TODO: Implement start test run dialog
            console.log('Start test run clicked')
          }}
        >
          Start New Test Run
        </Button>
      </Box>
      
      <Paper sx={{ height: 600, width: '100%' }}>
        <DataGrid
          rows={testRuns}
          columns={columns}
          pageSize={pageSize}
          onPageSizeChange={(newPageSize) => setPageSize(newPageSize)}
          rowsPerPageOptions={[5, 10, 20]}
          disableSelectionOnClick
        />
      </Paper>
    </Box>
  )
}