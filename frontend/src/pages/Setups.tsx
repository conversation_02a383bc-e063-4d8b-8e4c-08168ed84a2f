import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { useNavigate } from 'react-router-dom'
import {
  Box,
  Typography,
  Button,
  Paper,
  LinearProgress
} from '@mui/material'
import { DataGrid, GridColDef } from '@mui/x-data-grid'
import AddIcon from '@mui/icons-material/Add'
import SettingsIcon from '@mui/icons-material/Settings'
import axios from 'axios'

interface Agent {
  id: number
  name: string
  ip_address: string
  is_primary: boolean
}

interface Server {
  id: number
  name: string
  hostname: string
  server_type: string
}

interface Setup {
  id: number
  name: string
  description: string
  status: string
  created_at: string
  agents: Agent[]
  servers: Server[]
}

const columns: GridColDef[] = [
  { field: 'id', headerName: 'ID', width: 90 },
  { field: 'name', headerName: 'Setup Name', width: 200 },
  {
    field: 'servers',
    headerName: 'Servers',
    width: 200,
    renderCell: (params) => (
      <Box>
        {params.value?.map((server: Server) => (
          <Box key={server.id} sx={{ fontSize: '0.8em' }}>
            {server.name} ({server.server_type.toUpperCase()})
          </Box>
        ))}
      </Box>
    ),
  },
  {
    field: 'agents',
    headerName: 'Agents',
    width: 200,
    renderCell: (params) => (
      <Box>
        {params.value?.map((agent: Agent) => (
          <Box key={agent.id} sx={{ fontSize: '0.8em' }}>
            {agent.name} ({agent.ip_address})
          </Box>
        ))}
      </Box>
    ),
  },
  {
    field: 'status',
    headerName: 'Status',
    width: 120,
    renderCell: (params) => (
      <Box
        sx={{
          backgroundColor: params.value === 'idle' ? 'success.main' : 'warning.main',
          color: 'white',
          px: 2,
          py: 0.5,
          borderRadius: 1,
        }}
      >
        {params.value?.toUpperCase()}
      </Box>
    ),
  },
  {
    field: 'created_at',
    headerName: 'Created',
    width: 180,
    valueFormatter: (params) => new Date(params.value).toLocaleString(),
  },
]

export default function Setups() {
  const [pageSize, setPageSize] = useState(10)
  const navigate = useNavigate()

  const { data: setups = [], isLoading } = useQuery<Setup[]>({
    queryKey: ['setups'],
    queryFn: async () => {
      const response = await axios.get('/api/v1/setups/')
      return response.data
    },
  })

  if (isLoading) {
    return <LinearProgress />
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
        <Typography variant="h4">Test Setups</Typography>
        <Box>
          <Button
            variant="outlined"
            startIcon={<SettingsIcon />}
            onClick={() => navigate('/setup-management')}
            sx={{ mr: 1 }}
          >
            Manage Setups
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => navigate('/setup-management')}
          >
            Add Setup
          </Button>
        </Box>
      </Box>
      
      <Paper sx={{ height: 600, width: '100%' }}>
        <DataGrid
          rows={setups}
          columns={columns}
          pageSize={pageSize}
          onPageSizeChange={(newPageSize) => setPageSize(newPageSize)}
          rowsPerPageOptions={[5, 10, 20]}
          checkboxSelection
          disableSelectionOnClick
        />
      </Paper>
    </Box>
  )
}