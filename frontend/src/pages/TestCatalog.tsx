import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  Checkbox,
  Button,
  Chip,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  IconButton,
  Collapse,
  Alert,
  CircularProgress,
  Toolbar,
  <PERSON>ltip,
  Stack,
} from '@mui/material';
import {
  KeyboardArrowDown,
  KeyboardArrowUp,
  PlayArrow,
  SelectAll,
  FilterList,
  Search,
  Refresh,
} from '@mui/icons-material';
import { useParams, useNavigate } from 'react-router-dom';
import TestExecutionDialog from '../components/TestExecutionDialog';

interface Test {
  name: string;
  category: string;
  status?: string;
  duration?: number;
  lastRun?: string;
}

interface TestCategory {
  name: string;
  tests: Test[];
  expanded: boolean;
}

interface Agent {
  id: number;
  name: string;
  ip_address: string;
}

export default function TestCatalog() {
  const { setupId } = useParams<{ setupId: string }>();
  const navigate = useNavigate();
  const [agents, setAgents] = useState<Agent[]>([]);
  const [selectedAgent, setSelectedAgent] = useState<number | null>(null);
  const [categories, setCategories] = useState<TestCategory[]>([]);
  const [selectedTests, setSelectedTests] = useState<Set<string>>(new Set());
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [executionDialogOpen, setExecutionDialogOpen] = useState(false);

  // Fetch agents for the setup or all agents
  useEffect(() => {
    fetchAgents();
  }, [setupId]);

  // Fetch tests when agent is selected
  useEffect(() => {
    if (selectedAgent) {
      fetchTests();
    }
  }, [selectedAgent]);

  const fetchAgents = async () => {
    try {
      const url = setupId ? `/api/v1/agents/?setup_id=${setupId}` : '/api/v1/agents/';
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      setAgents(data);
      // Auto-select first agent
      if (data.length > 0 && !selectedAgent) {
        setSelectedAgent(data[0].id);
      }
    } catch (err) {
      console.error('Error fetching agents:', err);
      setError(`Failed to fetch agents: ${err.message}`);
    }
  };

  const fetchTests = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch(`/api/v1/agents/${selectedAgent}/available-tests`);
      if (!response.ok) throw new Error('Failed to fetch tests');
      
      const data = await response.json();
      
      // Transform categories into our format
      const categoryList: TestCategory[] = Object.entries(data.categories).map(([name, tests]) => ({
        name,
        tests: (tests as string[]).map(testName => ({
          name: testName,
          category: name,
          status: 'not_run',
        })),
        expanded: false,
      }));
      
      setCategories(categoryList);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch tests');
    } finally {
      setLoading(false);
    }
  };

  const handleSelectAll = (category: TestCategory) => {
    const categoryTests = category.tests.map(t => t.name);
    const allSelected = categoryTests.every(test => selectedTests.has(test));
    
    if (allSelected) {
      // Deselect all in category
      const newSelected = new Set(selectedTests);
      categoryTests.forEach(test => newSelected.delete(test));
      setSelectedTests(newSelected);
    } else {
      // Select all in category
      setSelectedTests(new Set([...selectedTests, ...categoryTests]));
    }
  };

  const handleSelectTest = (testName: string) => {
    const newSelected = new Set(selectedTests);
    if (newSelected.has(testName)) {
      newSelected.delete(testName);
    } else {
      newSelected.add(testName);
    }
    setSelectedTests(newSelected);
  };

  const handleToggleCategory = (categoryName: string) => {
    setCategories(categories.map(cat => 
      cat.name === categoryName ? { ...cat, expanded: !cat.expanded } : cat
    ));
  };

  const handleRunSelected = () => {
    if (selectedTests.size === 0) {
      setError('Please select at least one test');
      return;
    }
    setExecutionDialogOpen(true);
  };

  const handleExecuteTests = async (config: any) => {
    try {
      // Get setup_id from the selected agent
      const selectedAgentData = agents.find(a => a.id === selectedAgent);
      const actualSetupId = setupId || selectedAgentData?.setup_id;
      
      if (!actualSetupId) {
        setError('No setup selected');
        return;
      }

      // Create a runlist
      const runlistData = {
        name: config.name,
        description: config.description,
        setup_id: parseInt(actualSetupId.toString()),
        content: {
          tests: config.tests.map((test: string) => ({
            name: test,
            enabled: true
          })),
          parameters: config.parameters || {}
        }
      };

      console.log('Creating runlist with data:', runlistData);

      // Create runlist
      const runlistResponse = await fetch('/api/v1/runlists/', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(runlistData),
      });

      if (!runlistResponse.ok) {
        const errorText = await runlistResponse.text();
        console.error('Runlist response error:', errorText);
        throw new Error(`Failed to create runlist: ${runlistResponse.status} ${errorText}`);
      }
      const runlist = await runlistResponse.json();

      // Execute runlist
      const executeResponse = await fetch(`/api/v1/runlists/${runlist.id}/execute`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          test_names: config.tests,
          parameters_override: config.parameters
        }),
      });

      if (!executeResponse.ok) {
        const errorText = await executeResponse.text();
        console.error('Execute response error:', errorText);
        throw new Error(`Failed to execute runlist: ${executeResponse.status} ${errorText}`);
      }
      const execution = await executeResponse.json();

      // Navigate to test run details
      navigate(`/test-runs/${execution.test_run_id}`);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to execute tests');
    }
  };

  const filteredCategories = categories
    .filter(cat => categoryFilter === 'all' || cat.name === categoryFilter)
    .map(cat => ({
      ...cat,
      tests: cat.tests.filter(test => 
        test.name.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }))
    .filter(cat => cat.tests.length > 0);

  const totalTests = categories.reduce((sum, cat) => sum + cat.tests.length, 0);
  const filteredTests = filteredCategories.reduce((sum, cat) => sum + cat.tests.length, 0);

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Test Catalog
      </Typography>

      {/* Agent Selection */}
      <Paper sx={{ p: 2, mb: 2 }}>
        <FormControl fullWidth>
          <InputLabel>Select Agent</InputLabel>
          <Select
            value={selectedAgent || ''}
            onChange={(e) => setSelectedAgent(Number(e.target.value))}
            label="Select Agent"
          >
            {agents.map(agent => (
              <MenuItem key={agent.id} value={agent.id}>
                {agent.name} ({agent.ip_address})
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Paper>

      {/* Toolbar */}
      <Paper sx={{ mb: 2 }}>
        <Toolbar>
          <TextField
            placeholder="Search tests..."
            variant="outlined"
            size="small"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: <Search sx={{ mr: 1, color: 'action.active' }} />,
            }}
            sx={{ mr: 2, minWidth: 300 }}
          />
          
          <FormControl size="small" sx={{ minWidth: 200, mr: 2 }}>
            <InputLabel>Category</InputLabel>
            <Select
              value={categoryFilter}
              onChange={(e) => setCategoryFilter(e.target.value)}
              label="Category"
              startAdornment={<FilterList sx={{ mr: 1, color: 'action.active' }} />}
            >
              <MenuItem value="all">All Categories</MenuItem>
              {categories.map(cat => (
                <MenuItem key={cat.name} value={cat.name}>
                  {cat.name} ({cat.tests.length})
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <Box sx={{ flexGrow: 1 }} />

          <Typography variant="body2" sx={{ mr: 2 }}>
            {selectedTests.size} of {filteredTests} tests selected
          </Typography>

          <Tooltip title="Refresh test list">
            <IconButton onClick={fetchTests} disabled={!selectedAgent || loading}>
              <Refresh />
            </IconButton>
          </Tooltip>

          <Button
            variant="contained"
            startIcon={<PlayArrow />}
            onClick={handleRunSelected}
            disabled={selectedTests.size === 0}
          >
            Run Selected Tests
          </Button>
        </Toolbar>
      </Paper>

      {/* Error/Loading States */}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {loading && (
        <Box display="flex" justifyContent="center" p={4}>
          <CircularProgress />
        </Box>
      )}

      {/* Test Table */}
      {!loading && filteredCategories.length > 0 && (
        <TableContainer component={Paper}>
          <Table size="small">
            <TableHead>
              <TableRow>
                <TableCell width={50} />
                <TableCell width={50}>
                  <Checkbox
                    indeterminate={selectedTests.size > 0 && selectedTests.size < totalTests}
                    checked={selectedTests.size === totalTests && totalTests > 0}
                    onChange={() => {
                      if (selectedTests.size === totalTests) {
                        setSelectedTests(new Set());
                      } else {
                        const allTests = categories.flatMap(cat => cat.tests.map(t => t.name));
                        setSelectedTests(new Set(allTests));
                      }
                    }}
                  />
                </TableCell>
                <TableCell>Test Name</TableCell>
                <TableCell width={120}>Status</TableCell>
                <TableCell width={100}>Duration</TableCell>
                <TableCell width={150}>Last Run</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredCategories.map(category => (
                <React.Fragment key={category.name}>
                  {/* Category Header Row */}
                  <TableRow sx={{ bgcolor: 'action.hover' }}>
                    <TableCell>
                      <IconButton
                        size="small"
                        onClick={() => handleToggleCategory(category.name)}
                      >
                        {category.expanded ? <KeyboardArrowUp /> : <KeyboardArrowDown />}
                      </IconButton>
                    </TableCell>
                    <TableCell>
                      <Checkbox
                        indeterminate={
                          category.tests.some(t => selectedTests.has(t.name)) &&
                          !category.tests.every(t => selectedTests.has(t.name))
                        }
                        checked={category.tests.every(t => selectedTests.has(t.name))}
                        onChange={() => handleSelectAll(category)}
                      />
                    </TableCell>
                    <TableCell colSpan={4}>
                      <Stack direction="row" spacing={1} alignItems="center">
                        <Typography variant="subtitle1" fontWeight="bold">
                          {category.name}
                        </Typography>
                        <Chip label={`${category.tests.length} tests`} size="small" />
                        <Tooltip title="Select all in category">
                          <IconButton 
                            size="small" 
                            onClick={() => handleSelectAll(category)}
                          >
                            <SelectAll />
                          </IconButton>
                        </Tooltip>
                      </Stack>
                    </TableCell>
                  </TableRow>

                  {/* Test Rows */}
                  <TableRow>
                    <TableCell colSpan={6} sx={{ p: 0 }}>
                      <Collapse in={category.expanded}>
                        <Table size="small">
                          <TableBody>
                            {category.tests.map(test => (
                              <TableRow key={test.name}>
                                <TableCell width={50} />
                                <TableCell width={50}>
                                  <Checkbox
                                    checked={selectedTests.has(test.name)}
                                    onChange={() => handleSelectTest(test.name)}
                                  />
                                </TableCell>
                                <TableCell>
                                  <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                                    {test.name}
                                  </Typography>
                                </TableCell>
                                <TableCell width={120}>
                                  <Chip 
                                    label={test.status || 'Not Run'} 
                                    size="small"
                                    color={
                                      test.status === 'passed' ? 'success' :
                                      test.status === 'failed' ? 'error' :
                                      'default'
                                    }
                                  />
                                </TableCell>
                                <TableCell width={100}>
                                  {test.duration ? `${test.duration}s` : '-'}
                                </TableCell>
                                <TableCell width={150}>
                                  {test.lastRun || '-'}
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </Collapse>
                    </TableCell>
                  </TableRow>
                </React.Fragment>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      {!loading && filteredCategories.length === 0 && selectedAgent && (
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <Typography color="text.secondary">
            No tests found matching your criteria
          </Typography>
        </Paper>
      )}

      {/* Test Execution Dialog */}
      <TestExecutionDialog
        open={executionDialogOpen}
        onClose={() => setExecutionDialogOpen(false)}
        selectedTests={Array.from(selectedTests)}
        setupId={setupId || agents.find(a => a.id === selectedAgent)?.setup_id?.toString()}
        onExecute={handleExecuteTests}
      />
    </Box>
  );
}