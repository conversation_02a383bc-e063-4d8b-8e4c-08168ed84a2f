import { Routes, Route } from 'react-router-dom'
import { Box } from '@mui/material'

import Layout from './components/Layout'
import Dashboard from './pages/Dashboard'
import Setups from './pages/Setups'
import SetupDetail from './pages/SetupDetail'
import SetupManagement from './pages/SetupManagement'
import ServerManagement from './pages/ServerManagement'
import Tests from './pages/Tests'
import TestRuns from './pages/TestRuns'
import TestCatalog from './pages/TestCatalog'

function App() {
  return (
    <Box sx={{ display: 'flex', height: '100vh' }}>
      <Layout>
        <Routes>
          <Route path="/" element={<Dashboard />} />
          <Route path="/setups" element={<Setups />} />
          <Route path="/setups/:id" element={<SetupDetail />} />
          <Route path="/setup-management" element={<SetupManagement />} />
          <Route path="/server-management" element={<ServerManagement />} />
          <Route path="/tests" element={<Tests />} />
          <Route path="/test-runs" element={<TestRuns />} />
          <Route path="/test-catalog" element={<TestCatalog />} />
          <Route path="/test-catalog/:setupId" element={<TestCatalog />} />
        </Routes>
      </Layout>
    </Box>
  )
}

export default App