{"name": "cedori-dashboard", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.15.3", "@mui/material": "^5.15.3", "@mui/x-data-grid": "^6.18.7", "@tanstack/react-query": "^5.17.9", "axios": "^1.6.5", "notistack": "^3.0.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.21.1", "recharts": "^2.10.4", "socket.io-client": "^4.7.4"}, "devDependencies": {"@types/react": "^18.2.47", "@types/react-dom": "^18.2.18", "@typescript-eslint/eslint-plugin": "^6.18.1", "@typescript-eslint/parser": "^6.18.1", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.56.0", "eslint-plugin-react": "^7.37.5", "typescript": "^5.3.3", "vite": "^5.0.11"}, "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0"}}