import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// Get the backend URL from environment or default to localhost
const getBackendUrl = () => {
  // If VITE_BACKEND_URL is set, use it
  if (process.env.VITE_BACKEND_URL) {
    return process.env.VITE_BACKEND_URL
  }

  // If running in development and HOST is set, use the host IP
  if (process.env.NODE_ENV === 'development' && process.env.HOST) {
    return `http://${process.env.HOST}:8000`
  }

  // Default to localhost
  return 'http://localhost:8000'
}

const backendUrl = getBackendUrl()
const wsUrl = backendUrl.replace('http://', 'ws://')

console.log(`Frontend will proxy API calls to: ${backendUrl}`)

export default defineConfig({
  plugins: [react()],
  server: {
    port: 3000,
    host: true,
    proxy: {
      '/api': {
        target: backendUrl,
        changeOrigin: true,
        secure: false,
      },
      '/ws': {
        target: wsUrl,
        ws: true,
        changeOrigin: true,
      },
    },
  },
  define: {
    // Make the backend URL available to the frontend code
    __BACKEND_URL__: JSON.stringify(backendUrl),
  },
})