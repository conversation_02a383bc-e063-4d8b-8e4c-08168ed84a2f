#!/usr/bin/env python3
"""
Test script for the new setup management functionality.
This script tests the creation of setups with servers and agents.
"""

import requests
import json

BASE_URL = "http://localhost:8000/api/v1"

def test_create_setup_with_servers_and_agents():
    """Test creating a complete setup with servers and agents."""
    
    # 1. Create a setup
    setup_data = {
        "name": "Shelf66",
        "description": "VMware NIC certification test environment for Shelf 66"
    }
    
    print("Creating setup...")
    response = requests.post(f"{BASE_URL}/setups", json=setup_data)
    if response.status_code != 200:
        print(f"Failed to create setup: {response.status_code} - {response.text}")
        return
    
    setup = response.json()
    setup_id = setup["id"]
    print(f"Created setup with ID: {setup_id}")
    
    # 2. Add servers to the setup
    servers_data = [
        {
            "name": "shelf66sut",
            "hostname": "shelf66sut.esxcert.local",
            "server_type": "sut"
        },
        {
            "name": "shelf66aux",
            "hostname": "shelf66aux.esxcert.local",
            "server_type": "aux"
        }
    ]
    
    print("Adding servers...")
    response = requests.post(f"{BASE_URL}/setups/{setup_id}/servers", json=servers_data)
    if response.status_code != 200:
        print(f"Failed to add servers: {response.status_code} - {response.text}")
        return
    
    servers = response.json()
    print(f"Added {len(servers)} servers")
    
    # 3. Add agents to the setup
    agents_data = [
        {
            "name": "shelf66sut",
            "ip_address": "**************",
            "port": 22,
            "username": "root",
            "password": "vmware",
            "is_primary": True
        },
        {
            "name": "shelf66aux",
            "ip_address": "**************",
            "port": 22,
            "username": "root",
            "password": "vmware",
            "is_primary": False
        }
    ]
    
    print("Adding agents...")
    response = requests.post(f"{BASE_URL}/setups/{setup_id}/agents", json=agents_data)
    if response.status_code != 200:
        print(f"Failed to add agents: {response.status_code} - {response.text}")
        return
    
    agents = response.json()
    print(f"Added {len(agents)} agents")
    
    # 4. Verify the complete setup
    print("Verifying complete setup...")
    response = requests.get(f"{BASE_URL}/setups/{setup_id}")
    if response.status_code != 200:
        print(f"Failed to get setup: {response.status_code} - {response.text}")
        return
    
    complete_setup = response.json()
    print(f"Setup verification:")
    print(f"  Name: {complete_setup['name']}")
    print(f"  Servers: {len(complete_setup.get('servers', []))}")
    print(f"  Agents: {len(complete_setup.get('agents', []))}")
    
    return setup_id

def test_list_setups():
    """Test listing all setups."""
    print("\nListing all setups...")
    response = requests.get(f"{BASE_URL}/setups")
    if response.status_code != 200:
        print(f"Failed to list setups: {response.status_code} - {response.text}")
        return
    
    setups = response.json()
    print(f"Found {len(setups)} setups:")
    for setup in setups:
        print(f"  - {setup['name']} (ID: {setup['id']})")

def test_list_servers():
    """Test listing all servers."""
    print("\nListing all servers...")
    response = requests.get(f"{BASE_URL}/servers")
    if response.status_code != 200:
        print(f"Failed to list servers: {response.status_code} - {response.text}")
        return
    
    servers = response.json()
    print(f"Found {len(servers)} servers:")
    for server in servers:
        print(f"  - {server['name']} ({server['server_type']}) - {server['hostname']}")

if __name__ == "__main__":
    print("Testing Setup Management API...")
    print("=" * 50)
    
    try:
        # Test creating a complete setup
        setup_id = test_create_setup_with_servers_and_agents()
        
        # Test listing functionality
        test_list_setups()
        test_list_servers()
        
        print("\n" + "=" * 50)
        print("All tests completed successfully!")
        
    except requests.exceptions.ConnectionError:
        print("Error: Could not connect to the API server.")
        print("Make sure the backend is running on http://localhost:8000")
    except Exception as e:
        print(f"Error: {e}")
