{"environment": {"hosts": [{"hosttype": "esx", "hostname": "shelf65sut.esxcert.local", "user": "root", "password": "ca$hc0w"}, {"hosttype": "esx", "hostname": "shelf65aux.esxcert.local", "user": "root", "password": "ca$hc0w"}]}, "numHosts": 2, "params": {"testParams": [{"params_display": {"FQDNHost": {"Description": "Enter FQDN of Host under test", "type": "textbox", "value": null, "info": null}, "NFSNAME": {"Description": "Enter NFS server host name", "type": "textbox", "value": null, "info": null}, "NFSPATH": {"Description": "Enter NFS mount path (e.g. /root/)", "type": "textbox", "value": null, "info": null}}, "name": "Test::Configuration", "params": {"NFSPATH": null, "NFSNAME": null, "FQDNHost": null}}, {"params_display": {"FQDNHost": {"Description": "Enter FQDN of host under test (e.g blr-km-haas-eeqa-14.eng.vmw.com)", "type": "textbox", "value": null, "info": null}, "VCSERVER": {"Description": "Enter the VCenter server IP address (e.g *************)", "type": "textbox", "value": null, "info": null}, "VCSUSER": {"Description": "Enter the VCenter server User name (e.g <EMAIL>)", "type": "textbox", "value": null, "info": null}, "VCSPWD": {"Description": "Enter the VCenter server Password (e.g Password!23)", "type": "textbox", "value": null, "info": null}, "ESXIBUILD": {"Description": "Enter the ESXi build number to upgrade ESXi(e.g 19838288)", "type": "textbox", "value": null, "info": null}, "ESXIVERSION": {"Description": "Enter the ESXi version to upgrade ESXi(e.g 8.0.0-1.0.19838288)", "type": "textbox", "value": null, "info": null}, "EPKVMIP": {"Description": "Enter the EPK installed VM IPv4 address (e.g ************)", "type": "textbox", "value": null, "info": null}, "EPKVMIPUSER": {"Description": "Enter the EPK installed VM Username (e.g root)", "type": "textbox", "value": null, "info": null}, "EPKVMIPPWD": {"Description": "Enter the EPK installed VM Password (e.g Paswrd)", "type": "textbox", "value": null, "info": null}, "ESXiDEPOT": {"Description": "Enter the ESXi depot name located in EPK installed VM to upgrade ESXi(e.g VMware-ESXi-8.0-19838288-depot.zip)", "type": "textbox", "value": null, "info": null}, "CUSDEPOT": {"Description": "Enter the component located in EPK installed VM (e.g Intel-icen_1.6.6.0-1OEM.700.1.0.15843807_19311607.zip)", "type": "textbox", "value": null, "info": null}, "HTTPFOLDER": {"Description": "Enter the component and ESXi depot located path in EPK installed VM (e.g /root/Upgrade)", "type": "textbox", "value": null, "info": null}, "ADDONNAME": {"Description": "Enter the name of the addon (e.g icen)", "type": "textbox", "value": null, "info": null}, "ADDONVERSION": {"Description": "Enter the version of the addon (e.g 2.0.0-0)", "type": "textbox", "value": null, "info": null}}, "name": "vLCM-ISO-Upgrade", "params": {"VCSPWD": null, "EPKVMIPUSER": null, "EPKVMIP": null, "ESXIVERSION": null, "VCSERVER": null, "ADDONVERSION": null, "EPKVMIPPWD": null, "CUSDEPOT": null, "ESXiDEPOT": null, "VCSUSER": null, "HTTPFOLDER": null, "ESXIBUILD": null, "ADDONNAME": null, "FQDNHost": null}}, {"params_display": {"FQDNHost": {"Description": "Enter FQDN of Host under test", "type": "textbox", "value": null, "info": null}, "TESTDRIVER": {"Description": "Enter Driver name", "type": "textbox", "value": null, "info": null}, "NFSNAME": {"Description": "Enter NFS server host name", "type": "textbox", "value": null, "info": null}, "NFSPATH": {"Description": "Enter NFS mount path (e.g. /root/)", "type": "textbox", "value": null, "info": null}, "VIBFILE": {"Description": "Upload the component file", "type": "file", "value": null, "info": null}, "VIBMODS": {"Description": "Enter list of Vib Modules, comma separated (e.g nmlx4-rdma,nmlx-rdma)", "type": "textbox", "value": null, "info": null}, "VIBVERSION": {"Description": "Enter list of Vib Versions, comma separated (e.g *********-1vmw.687.0.0.13394445,*********-1vmw.687.0.0.21194445)", "type": "textbox", "value": null, "info": null}, "COMPONENTNAME": {"Description": "Enter test component name (e.g nmlx4_rdma)", "type": "textbox", "value": null, "info": null}, "COMPVERSION": {"Description": "Enter test component version (e.g *********-1vmw.687.0.0.13394445)", "type": "textbox", "value": null, "info": null}, "COMPPATH": {"Description": "Enter test component path (e.g /vmfs/volumes/datastore/nmlx4_rdma-*********-1vmw.687.0.0.13394445.zip)", "type": "textbox", "value": null, "info": null}, "ACCEPTANCELEVEL": {"Description": "Enter the acceptancelevel (e.g VMwareCertified)", "type": "textbox", "value": null, "info": null}, "DATASTORE": {"Description": "Enter FQDN host local datastore name", "type": "textbox", "value": null, "info": null}}, "name": "IUVT::Test::Parameters", "params": {"VIBFILE": null, "ACCEPTANCELEVEL": null, "NFSNAME": null, "VIBMODS": null, "NFSPATH": null, "COMPVERSION": null, "VIBVERSION": null, "COMPONENTNAME": null, "COMPPATH": null, "VIBFILE.url": null, "TESTDRIVER": null, "DATASTORE": null, "FQDNHost": null}}, {"params_display": {"FQDNHost": {"Description": "Enter FQDN of Host under test", "type": "textbox", "value": null, "info": null}, "TESTDRIVER": {"Description": "Enter Driver name", "type": "textbox", "value": null, "info": null}, "TESTNIC1": {"Description": "Enter first vmnic name (e.g vmnic1)", "type": "textbox", "value": null, "info": null}, "TESTNIC2": {"Description": "Enter second vmnic name (e.g vmnic2)", "type": "textbox", "value": null, "info": null}, "NFSNAME": {"Description": "Enter NFS server host name", "type": "textbox", "value": null, "info": null}, "NFSPATH": {"Description": "Enter NFS mount path (e.g. /root/)", "type": "textbox", "value": null, "info": null}, "VLAN": {"Description": "Enter IPV4 vlan id", "type": "textbox", "value": null, "info": null}, "IPv6VLAN": {"Description": "Enter IPv6 vlan id", "type": "textbox", "value": null, "info": null}, "FIRMWAREVERSION": {"Description": "Enter the device's firmware version. (Firmware version used in this certification)", "type": "textbox", "value": null, "info": null}}, "name": "Test::Parameters", "params": {"NFSPATH": null, "NFSNAME": null, "TESTNIC2": null, "VLAN": null, "TESTNIC1": null, "TESTDRIVER": null, "FIRMWAREVERSION": null, "FQDNHost": null, "IPv6VLAN": null}}, {"params_display": {"FQDNHost": {"Description": "Enter FQDN of Host under test", "type": "textbox", "value": null, "info": null}, "TESTDRIVER": {"Description": "Enter Driver name", "type": "textbox", "value": null, "info": null}, "NFSNAME": {"Description": "Enter NFS server host name", "type": "textbox", "value": null, "info": null}, "NFSPATH": {"Description": "Enter NFS mount path (e.g. /root/)", "type": "textbox", "value": null, "info": null}, "TESTNIC1": {"Description": "Enter first vmnic name (e.g vmnic1)", "type": "textbox", "value": null, "info": null}, "TESTNIC2": {"Description": "Enter second vmnic name (e.g vmnic2)", "type": "textbox", "value": null, "info": null}, "TESTNIC3": {"Description": "Enter third vmnic name (e.g vmnic3)", "type": "textbox", "value": null, "info": null}, "TESTNIC4": {"Description": "Enter fourth vmnic name (e.g vmnic4)", "type": "textbox", "value": null, "info": null}, "INTERFACE1": {"Description": "Enter the first Eth port Number", "type": "textbox", "value": null, "info": null}, "INTERFACE2": {"Description": "Enter the second Eth port Number", "type": "textbox", "value": null, "info": null}, "INTERFACE3": {"Description": "Enter the third Eth port Number", "type": "textbox", "value": null, "info": null}, "INTERFACE4": {"Description": "Enter the fourth Eth port Number", "type": "textbox", "value": null, "info": null}, "MODULE": {"Description": "Enter the Switch Module  (e.g  0, 1)", "type": "textbox", "value": null, "info": null}, "TYPE": {"Description": "Enter the Switch Port Speed (e.g  1G, 10G, 25G, 40G , 50G, 100G)", "type": "textbox", "value": null, "info": null}, "TESTSWITCH1": {"Description": "Enter the Switch IP", "type": "textbox", "value": null, "info": null}, "SWITCH_TYPE": {"Description": "Enter Manufacture Of The Switch (e.g  Brocade, Cisco, Extreme)", "type": "textbox", "value": null, "info": null}, "SWITCH_USER": {"Description": "Enter User Name to log in to switch", "type": "textbox", "value": null, "info": null}, "SWITCH_PASS": {"Description": "Enter Password to log in to switch", "type": "textbox", "value": null, "info": null}, "VLAN": {"Description": "Enter IPV4 vlan id", "type": "textbox", "value": null, "info": null}, "IPv6VLAN": {"Description": "Enter IPv6 vlan id", "type": "textbox", "value": null, "info": null}, "FIRMWAREVERSION": {"Description": "Enter the device's firmware version. (Firmware version used in this certification)", "type": "textbox", "value": null, "info": null}}, "name": "Test::Failover::Parameters", "params": {"TESTSWITCH1": null, "SWITCH_TYPE": null, "NFSNAME": null, "TESTNIC4": null, "TESTNIC3": null, "TESTNIC2": null, "VLAN": null, "TESTNIC1": null, "IPv6VLAN": null, "NFSPATH": null, "MODULE": null, "SWITCH_USER": null, "INTERFACE3": null, "INTERFACE4": null, "INTERFACE1": null, "INTERFACE2": null, "SWITCH_PASS": null, "TESTDRIVER": null, "TYPE": null, "FIRMWAREVERSION": null, "FQDNHost": null}}, {"params_display": {"FQDNHost": {"Description": "Enter FQDN of Host under test", "type": "textbox", "value": null, "info": null}, "TESTDRIVER": {"Description": "Enter Driver name", "type": "textbox", "value": null, "info": null}, "TESTNIC1": {"Description": "Enter first vmnic name (e.g vmnic1)", "type": "textbox", "value": null, "info": null}, "TESTNIC2": {"Description": "Enter second vmnic name (e.g vmnic2)", "type": "textbox", "value": null, "info": null}, "NFSNAME": {"Description": "Enter NFS server host name", "type": "textbox", "value": null, "info": null}, "NFSPATH": {"Description": "Enter NFS mount path (e.g. /root/)", "type": "textbox", "value": null, "info": null}, "VCSERVER": {"Description": "Enter The VCenter server IP address", "type": "textbox", "value": null, "info": null}, "VCSUSER": {"Description": "Enter The VCenter server User name", "type": "textbox", "value": null, "info": null}, "VCSPWD": {"Description": "Enter The VCenter server Password", "type": "textbox", "value": null, "info": null}, "VLAN": {"Description": "Enter IPV4 vlan id", "type": "textbox", "value": null, "info": null}, "IPv6VLAN": {"Description": "Enter IPv6 vlan id", "type": "textbox", "value": null, "info": null}, "FIRMWAREVERSION": {"Description": "Enter the device's firmware version. (Firmware version used in this certification)", "type": "textbox", "value": null, "info": null}}, "name": "Test::VC::Parameters", "params": {"NFSPATH": null, "VCSPWD": null, "VCSUSER": null, "NFSNAME": null, "TESTNIC2": null, "VCSERVER": null, "VLAN": null, "TESTNIC1": null, "TESTDRIVER": null, "FIRMWAREVERSION": null, "FQDNHost": null, "IPv6VLAN": null}}, {"params_display": {"FQDNHost": {"Description": "Enter FQDN of Host under test", "type": "textbox", "value": null, "info": null}, "AUXHost": {"Description": "Enter FQDN of Aux host under test", "type": "textbox", "value": null, "info": null}, "TESTDRIVER": {"Description": "Enter Driver name", "type": "textbox", "value": null, "info": null}, "NFSNAME": {"Description": "Enter NFS server host name", "type": "textbox", "value": null, "info": null}, "NFSPATH": {"Description": "Enter NFS mount path (e.g. /root/)", "type": "textbox", "value": null, "info": null}, "NFSPATHVMOTION": {"Description": "Enter NFS server path for vmotion", "type": "textbox", "value": null, "info": null}, "VCSERVER": {"Description": "Enter The VCenter server IP address", "type": "textbox", "value": null, "info": null}, "VCSUSER": {"Description": "Enter The VCenter server User name", "type": "textbox", "value": null, "info": null}, "VCSPWD": {"Description": "Enter The VCenter server Password", "type": "textbox", "value": null, "info": null}, "VLAN": {"Description": "Enter IPV4 vlan id", "type": "textbox", "value": null, "info": null}, "IPv6VLAN": {"Description": "Enter IPv6 vlan id", "type": "textbox", "value": null, "info": null}, "FIRMWAREVERSION": {"Description": "Enter the device's firmware version. (Firmware version used in this certification)", "type": "textbox", "value": null, "info": null}}, "name": "Test::Twohosts::VC::Parameters", "params": {"NFSPATH": null, "VCSPWD": null, "AUXHost": null, "VCSUSER": null, "NFSPATHVMOTION": null, "NFSNAME": null, "VCSERVER": null, "VLAN": null, "TESTDRIVER": null, "FIRMWAREVERSION": null, "FQDNHost": null, "IPv6VLAN": null}}, {"params_display": {"FQDNHost": {"Description": "Enter FQDN of Host under test", "type": "textbox", "value": null, "info": null}, "AUXHost": {"Description": "Enter FQDN of Aux host under test", "type": "textbox", "value": null, "info": null}, "TESTDRIVER": {"Description": "Enter Driver name", "type": "textbox", "value": null, "info": null}, "NFSNAME": {"Description": "Enter NFS server host name", "type": "textbox", "value": null, "info": null}, "NFSPATH": {"Description": "Enter NFS mount path (e.g. /root/)", "type": "textbox", "value": null, "info": null}, "VLAN": {"Description": "Enter IPV4 vlan id", "type": "textbox", "value": null, "info": null}, "IPv6VLAN": {"Description": "Enter IPv6 vlan id", "type": "textbox", "value": null, "info": null}, "FIRMWAREVERSION": {"Description": "Enter the device's firmware version. (Firmware version used in this certification)", "type": "textbox", "value": null, "info": null}}, "name": "Test::Twohosts::Parameters", "params": {"NFSPATH": null, "AUXHost": null, "NFSNAME": null, "VLAN": null, "TESTDRIVER": null, "FIRMWAREVERSION": null, "FQDNHost": null, "IPv6VLAN": null}}, {"params_display": {"FQDNHost": {"Description": "Enter FQDN of Host under test", "type": "textbox", "value": null, "info": null}, "AUXHost": {"Description": "Enter FQDN of Aux host under test", "type": "textbox", "value": null, "info": null}, "TESTDRIVER": {"Description": "Enter Driver name", "type": "textbox", "value": null, "info": null}, "NFSNAME": {"Description": "Enter NFS server host name", "type": "textbox", "value": null, "info": null}, "NFSPATH": {"Description": "Enter NFS mount path (e.g. /root/)", "type": "textbox", "value": null, "info": null}, "TESTNIC1": {"Description": "Enter first vmnic name (e.g vmnic1)", "type": "textbox", "value": null, "info": null}, "TESTNIC2": {"Description": "Enter second vmnic name (e.g vmnic2)", "type": "textbox", "value": null, "info": null}, "TESTNIC3": {"Description": "Enter third vmnic name (e.g vmnic3)", "type": "textbox", "value": null, "info": null}, "TESTNIC4": {"Description": "Enter fourth vmnic name (e.g vmnic4)", "type": "textbox", "value": null, "info": null}, "INTERFACE1": {"Description": "Enter the first Eth port Number", "type": "textbox", "value": null, "info": null}, "INTERFACE2": {"Description": "Enter the second Eth port Number", "type": "textbox", "value": null, "info": null}, "INTERFACE3": {"Description": "Enter the third Eth port Number", "type": "textbox", "value": null, "info": null}, "INTERFACE4": {"Description": "Enter the fourth Eth port Number", "type": "textbox", "value": null, "info": null}, "MODULE": {"Description": "Enter the Switch Module  (e.g  0, 1)", "type": "textbox", "value": null, "info": null}, "TYPE": {"Description": "Enter the Switch Port Speed (e.g  1G, 10G, 25G, 40G , 50G, 100G)", "type": "textbox", "value": null, "info": null}, "TESTSWITCH1": {"Description": "Enter the Switch IP", "type": "textbox", "value": null, "info": null}, "SWITCH_TYPE": {"Description": "Enter Manufacture Of The Switch (e.g  Brocade, Cisco, Extreme)", "type": "textbox", "value": null, "info": null}, "SWITCH_USER": {"Description": "Enter User Name to log in to switch", "type": "textbox", "value": null, "info": null}, "SWITCH_PASS": {"Description": "Enter Password to log in to switch", "type": "textbox", "value": null, "info": null}, "AUXNIC1": {"Description": "Enter vmnic name (e.g vmnic1) of AUXHost", "type": "textbox", "value": null, "info": null}, "AUXNIC2": {"Description": "Enter vmnic name (e.g vmnic2) of AUXHost", "type": "textbox", "value": null, "info": null}, "AUXINTERFACE1": {"Description": "Enter the first Eth port Number of AUXHost", "type": "textbox", "value": null, "info": null}, "AUXINTERFACE2": {"Description": "Enter the second Eth port Number of AUXHost", "type": "textbox", "value": null, "info": null}, "VLAN": {"Description": "Enter IPV4 vlan id", "type": "textbox", "value": null, "info": null}, "IPv6VLAN": {"Description": "Enter IPv6 vlan id", "type": "textbox", "value": null, "info": null}, "FIRMWAREVERSION": {"Description": "Enter the device's firmware version. (Firmware version used in this certification)", "type": "textbox", "value": null, "info": null}}, "name": "Test::Twohosts::Failover::Parameters", "params": {"TESTSWITCH1": null, "TESTNIC4": null, "TESTNIC3": null, "TESTNIC2": null, "VLAN": null, "TESTNIC1": null, "NFSPATH": null, "AUXNIC1": null, "AUXNIC2": null, "AUXHost": null, "INTERFACE3": null, "INTERFACE4": null, "INTERFACE1": null, "INTERFACE2": null, "AUXINTERFACE1": null, "TYPE": null, "AUXINTERFACE2": null, "SWITCH_TYPE": null, "NFSNAME": null, "IPv6VLAN": null, "MODULE": null, "SWITCH_USER": null, "SWITCH_PASS": null, "TESTDRIVER": null, "FIRMWAREVERSION": null, "FQDNHost": null}}, {"params_display": {"FQDNHost": {"Description": "Enter FQDN of Host under test", "type": "textbox", "value": null, "info": null}, "TESTDRIVER": {"Description": "Enter Driver name", "type": "textbox", "value": null, "info": null}, "NFSNAME": {"Description": "Enter NFS server host name", "type": "textbox", "value": null, "info": null}, "NFSPATH": {"Description": "Enter NFS mount path (e.g. /root/)", "type": "textbox", "value": null, "info": null}, "VIBMODS": {"Description": "Enter list of Vib Modules, comma separated (e.g nmlx4-rdma,nmlx-rdma)", "type": "textbox", "value": null, "info": null}, "VIBVERSION": {"Description": "Enter list of Vib Versions, comma separated (e.g *********-1vmw.687.0.0.13394445,*********-1vmw.687.0.0.21194445)", "type": "textbox", "value": null, "info": null}, "COMPONENTNAME": {"Description": "Enter test component name (e.g nmlx4_rdma)", "type": "textbox", "value": null, "info": null}, "COMPVERSION": {"Description": "Enter test component version (e.g *********-1vmw.687.0.0.13394445)", "type": "textbox", "value": null, "info": null}, "COMPPATH": {"Description": "Enter test component path (e.g /vmfs/volumes/datastore/nmlx4_rdma-*********-1vmw.687.0.0.13394445.zip)", "type": "textbox", "value": null, "info": null}, "ACCEPTANCELEVEL": {"Description": "Enter the acceptancelevel (e.g VMwareCertified)", "type": "textbox", "value": null, "info": null}, "DATASTORE": {"Description": "Enter FQDN host local datastore name", "type": "textbox", "value": null, "info": null}}, "name": "Test::IUVT::Parameters", "params": {"NFSPATH": null, "COMPVERSION": null, "VIBVERSION": null, "COMPONENTNAME": null, "ACCEPTANCELEVEL": null, "COMPPATH": null, "NFSNAME": null, "TESTDRIVER": null, "DATASTORE": null, "FQDNHost": null, "VIBMODS": null}}, {"params_display": {}, "name": "Asyncdriver::Functional::Api_complaint", "params": {}}, {"params_display": {}, "name": "Asyncdriver::Functional::Stacksizecheck", "params": {}}, {"params_display": {}, "name": "Asyncdriver::PreCheck_Component", "params": {}}, {"params_display": {}, "name": "Asyncdriver::Install_Component", "params": {}}, {"params_display": {}, "name": "Asyncdriver::Functional::Vib_VersionCheck", "params": {}}, {"params_display": {}, "name": "Asyncdriver::Functional::UEFISecureboot_check", "params": {}}, {"params_display": {}, "name": "Asyncdriver::Rollup_ISOTest", "params": {}}, {"params_display": {}, "name": "Uninstall::Component", "params": {}}, {"params_display": {}, "name": "DDV::Functional::EIRandomNoReload", "params": {}}, {"params_display": {}, "name": "DDV::Functional::StopNoReload", "params": {}}, {"params_display": {}, "name": "Networking::SRIOV::PTP::Functional::TRX", "params": {}}, {"params_display": {}, "name": "Networking::Geneve::ENS::Stress::Interrupt::RandomOperationIO", "params": {}}, {"params_display": {}, "name": "Networking::Geneve::ENS::Stress::Interrupt::Traffic", "params": {}}, {"params_display": {}, "name": "Networking::Geneve::ENS::Stress::Interrupt::Mix_Various_Traffic", "params": {}}, {"params_display": {}, "name": "Networking::Stress::ENS::Interrupt::RandomOperationIO", "params": {}}, {"params_display": {}, "name": "Networking::Stress::ENS::Interrupt::Reboot", "params": {}}, {"params_display": {}, "name": "Networking::Geneve::ENS::Functional::Interrupt::Offload_Legacy_IPv4", "params": {}}, {"params_display": {}, "name": "Networking::Geneve::ENS::Functional::Interrupt::Offload_Legacy_IPv6", "params": {}}, {"params_display": {}, "name": "Networking::Geneve::ENS::Functional::Interrupt::IPv4VGT", "params": {}}, {"params_display": {}, "name": "Networking::Geneve::ENS::Functional::Interrupt::Basic_DownUp", "params": {}}, {"params_display": {}, "name": "Networking::Geneve::ENS::Functional::Interrupt::ChangeMTU", "params": {}}, {"params_display": {}, "name": "Networking::Geneve::ENS::Functional::Interrupt::Offload_IPv6", "params": {}}, {"params_display": {}, "name": "Networking::Geneve::ENS::Functional::Interrupt::Teaming", "params": {}}, {"params_display": {}, "name": "Networking::Geneve::ENS::Functional::Interrupt::Uplink_Operate", "params": {}}, {"params_display": {}, "name": "Networking::Geneve::ENS::Functional::Interrupt::VMIPMAC_Change", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Interrupt::VMK_NFSReadWrite", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Interrupt::VMK_TrafficIPv4", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Interrupt::NUMA_Teaming_Failover", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Interrupt::NoVM_IPv6JF", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Interrupt::NoVM_IPv6Netperf", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Interrupt::NoVM_IPv6Ping", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Interrupt::NoVM_IPv6SCP", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Interrupt::NoVM_IPv6TSO", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Interrupt::NoVM_IPv6VlanTag", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Interrupt::IPv6::Checksum.Offload", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Interrupt::IPv6::Esxcli_DownUp", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Interrupt::IPv6::FloodPing", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Interrupt::IPv6::Iozone", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Interrupt::IPv6::MultiCast", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Interrupt::IPv6::Netperf", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Interrupt::IPv6::SCP", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Interrupt::IPv6::TSO", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Interrupt::IPv6::VLAN_RX", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Interrupt::IPv6::VLAN_TX", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Interrupt::IPv4VGT", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Interrupt::IPv6VGT", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Interrupt::<PERSON><PERSON><PERSON>", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Interrupt::Broadcast", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Interrupt::ChangeMTU_Reset", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Interrupt::FloodPing", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Interrupt::Iozone", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Interrupt::Multimode_ChangeMTU_Unload", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Interrupt::SCP", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Interrupt::NoVM_DefaultqRSS", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Interrupt::NoVM_ControlPathCommand", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Interrupt::NoVM_DeviceStateChange", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Interrupt::NoVM_DeviceStateDriverReload", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Interrupt::NoVM_DriverLoadUnLoad", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Interrupt::NoVM_DriverReloadWithParams", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Interrupt::NoVM_DriverReloadWithSpecParams", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Interrupt::NoVM_HardwareCheck", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Interrupt::NoVM_IPv4JF", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Interrupt::NoVM_IPv4Netperf", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Interrupt::NoVM_IPv4Ping", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Interrupt::NoVM_IPv4SCP", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Interrupt::NoVM_IPv4TSO", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Interrupt::NoVM_IPv4VlanTag", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Interrupt::NoVM_LoadEsx_Reboot", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Interrupt::NoVM_Multicast", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Interrupt::NoVM_NegativeDeviceStateChange", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Interrupt::NoVM_NegativeParallelOps", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Interrupt::NoVM_SwitchPortEnableDisable", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Interrupt::NoVM_VLAN_RX", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Interrupt::NoVM_VLAN_TX", "params": {}}, {"params_display": {}, "name": "Networking::Stress::ENS::Interrupt::NoVM_DeviceStateWithGracefulRemove", "params": {}}, {"params_display": {}, "name": "Networking::Geneve::ENS::Functional::Polling::IPv4VGT", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Polling::NUMA_Teaming_Failover", "params": {}}, {"params_display": {}, "name": "Networking::Geneve::ENS::Stress::Polling::Mix_Various_Traffic", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Polling::IPv6::SCP", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Polling::IPv6::MultiCast", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Polling::IPv6::VLAN_RX", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Polling::IPv6::Iozone", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Polling::IPv6::FloodPing", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Polling::IPv6::Checksum.Offload", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Polling::IPv6::VLAN_TX", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Polling::IPv6::TSO", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Polling::IPv6::Netperf", "params": {}}, {"params_display": {}, "name": "Networking::Stress::ENS::Polling::Reboot", "params": {}}, {"params_display": {}, "name": "Networking::Stress::ENS::Polling::RandomOperationIO", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Polling::Iozone", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Polling::FloodPing", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Polling::Blaster", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Polling::Broadcast", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Polling::ChangeEnsMode", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Polling::VMK_NFSReadWrite", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Polling::VMK_TrafficIPv4", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Polling::IPv4VGT", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Polling::IPv6VGT", "params": {}}, {"params_display": {}, "name": "Networking::Geneve::ENS::Stress::Polling::RandomOperationIO", "params": {}}, {"params_display": {}, "name": "Networking::Geneve::ENS::Stress::Polling::Traffic", "params": {}}, {"params_display": {}, "name": "Networking::Geneve::ENS::Functional::Polling::ChangeMTU", "params": {}}, {"params_display": {}, "name": "Networking::Geneve::ENS::Functional::Polling::AddDeleteUplink", "params": {}}, {"params_display": {}, "name": "Networking::Geneve::ENS::Functional::Polling::Teaming", "params": {}}, {"params_display": {}, "name": "Networking::Geneve::ENS::Functional::Polling::Offload_IPv4", "params": {}}, {"params_display": {}, "name": "Networking::Geneve::ENS::Functional::Polling::Offload_IPv6", "params": {}}, {"params_display": {}, "name": "Networking::Geneve::ENS::Functional::Polling::Basic_DownUp", "params": {}}, {"params_display": {}, "name": "Networking::Geneve::ENS::Functional::Polling::Uplink_Operate", "params": {}}, {"params_display": {}, "name": "Networking::Geneve::ENS::Functional::Polling::VMIPMAC_Change", "params": {}}, {"params_display": {}, "name": "Networking::Geneve::ENS::Functional::Polling::RXFilter", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Polling::NoVM_IPv6SCP", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Polling::NoVM_IPv6JF", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Polling::NoVM_IPv6Ping", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Polling::NoVM_IPv6Netperf", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Polling::NoVM_IPv6TSO", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Polling::NoVM_IPv6VlanTag", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Polling::NoVM_DeviceStateChange", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Polling::NoVM_VLAN_RX", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Polling::NoVM_DeviceStateDriverReload", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Polling::NoVM_IPv4TSO", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Polling::NoVM_SwitchPortEnableDisable", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Polling::NoVM_ControlPathCommand", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Polling::NoVM_NegativeDeviceStateChange", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Polling::NoVM_DriverLoadUnLoad", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Polling::NoVM_IPv4Ping", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Polling::NoVM_IPv4JF", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Polling::NoVM_IPv4Netperf", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Polling::NoVM_IPv4SCP", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Polling::NoVM_VLAN_TX", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Polling::NoVM_DriverReloadWithParams", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Polling::NoVM_DriverReloadWithSpecParams", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Polling::NoVM_Multicast", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Polling::NoVM_LoadEsx_Reboot", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Polling::NoVM_HardwareCheck", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Polling::NoVM_NegativeParallelOps", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Polling::NoVM_IPv4VlanTag", "params": {}}, {"params_display": {}, "name": "Networking::Stress::ENS::Polling::NoVM_DeviceStateWithGracefulRemove", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Polling::NoVM_DefaultqRSS", "params": {}}, {"params_display": {}, "name": "Networking::Geneve::ENS::Functional::Interrupt::RXFilter", "params": {}}, {"params_display": {}, "name": "Networking::Functional::NoVM_LACP", "params": {}}, {"params_display": {}, "name": "Networking::SRIOV::Functional::NoVM_DeviceStateChange", "params": {}}, {"params_display": {}, "name": "Networking::SRIOV::Functional::NoVM_NegativeDeviceStateChange", "params": {}}, {"params_display": {}, "name": "Networking::SRIOV::Functional::NoVM_PF_IPv4TSO", "params": {}}, {"params_display": {}, "name": "Networking::SRIOV::Functional::PrimaryPFDeviceState", "params": {}}, {"params_display": {}, "name": "Networking::Geneve::Functional::IPv4VGT", "params": {}}, {"params_display": {}, "name": "Networking::Functional::MultipleUplink_Ops", "params": {}}, {"params_display": {}, "name": "Networking::SRIOV::Stress::RandomOperationIO", "params": {}}, {"params_display": {}, "name": "Networking::Geneve::Functional::LACP", "params": {}}, {"params_display": {}, "name": "Networking::Geneve::Stress::Traffic", "params": {}}, {"params_display": {}, "name": "Networking::Geneve::Stress::RandomOperationIO", "params": {}}, {"params_display": {}, "name": "Networking::SRIOV::Functional::VMOPs", "params": {}}, {"params_display": {}, "name": "Networking::SRIOV::Functional::PF_VMotion", "params": {}}, {"params_display": {}, "name": "Networking::Functional::IPv6::VMOPs", "params": {}}, {"params_display": {}, "name": "Networking::VXLAN::Stress::RandomOperationIO", "params": {}}, {"params_display": {}, "name": "Networking::VXLAN::Stress::Traffic", "params": {}}, {"params_display": {}, "name": "Networking::Functional::WOL", "params": {}}, {"params_display": {}, "name": "Networking::VXLAN::Functional::IPv4VGT", "params": {}}, {"params_display": {}, "name": "Networking::Functional::IPv6::Netqueue", "params": {}}, {"params_display": {}, "name": "Networking::Functional::NoVM_SameMac_Filter", "params": {}}, {"params_display": {}, "name": "Networking::Geneve::Functional::NoVM_ChangeUdpPort", "params": {}}, {"params_display": {}, "name": "Networking::Geneve::Functional::NoVM_Option", "params": {}}, {"params_display": {}, "name": "Networking::Functional::NoVM_IPv6JF", "params": {}}, {"params_display": {}, "name": "Networking::Functional::NoVM_IPv6Ping", "params": {}}, {"params_display": {}, "name": "Networking::Functional::NoVM_IPv6VlanTag", "params": {}}, {"params_display": {}, "name": "Networking::Functional::NoVM_IPv6Netperf", "params": {}}, {"params_display": {}, "name": "Networking::Functional::NoVM_IPv6TSO", "params": {}}, {"params_display": {}, "name": "Networking::Functional::NoVM_IPv6RandomPktInject", "params": {}}, {"params_display": {}, "name": "Networking::Functional::NoVM_IPv6SCP", "params": {}}, {"params_display": {}, "name": "Networking::Functional::NoVM_DefaultqRSS", "params": {}}, {"params_display": {}, "name": "Networking::Functional::NoVM_DeviceRSS", "params": {}}, {"params_display": {}, "name": "Networking::Functional::NoVM_DriverLoadUnLoad", "params": {}}, {"params_display": {}, "name": "Networking::Functional::NoVM_VLAN_TX", "params": {}}, {"params_display": {}, "name": "Networking::Functional::NoVM_HW_Cap", "params": {}}, {"params_display": {}, "name": "Networking::Functional::NoVM_IPChecksumOffload", "params": {}}, {"params_display": {}, "name": "Networking::Functional::NoVM_IPv4SCP", "params": {}}, {"params_display": {}, "name": "Networking::Functional::NoVM_IPv4VlanTag", "params": {}}, {"params_display": {}, "name": "Networking::Functional::NoVM_IPv4JF", "params": {}}, {"params_display": {}, "name": "Networking::Functional::NoVM_RSS", "params": {}}, {"params_display": {}, "name": "Networking::Functional::NoVM_SwitchPortEnableDisable", "params": {}}, {"params_display": {}, "name": "Networking::Functional::NoVM_SpeedDuplex", "params": {}}, {"params_display": {}, "name": "Networking::Functional::NoVM_IPv4Netperf", "params": {}}, {"params_display": {}, "name": "Networking::Functional::NoVM_DriverReloadWithParams", "params": {}}, {"params_display": {}, "name": "Networking::Functional::NoVM_DriverReloadWithSpecParams", "params": {}}, {"params_display": {}, "name": "Networking::Functional::NoVM_IPv4TSO", "params": {}}, {"params_display": {}, "name": "Networking::Functional::NoVM_DeviceStateChange", "params": {}}, {"params_display": {}, "name": "Networking::Functional::NoVM_IPv4RandomPktInject", "params": {}}, {"params_display": {}, "name": "Networking::Functional::NoVM_LoadEsx_Reboot", "params": {}}, {"params_display": {}, "name": "Networking::Functional::NoVM_NegativeDeviceStateChange", "params": {}}, {"params_display": {}, "name": "Networking::Functional::NoVM_MultiRSS", "params": {}}, {"params_display": {}, "name": "Networking::Functional::NoVM_IPv4GenEncapPktInject", "params": {}}, {"params_display": {}, "name": "Networking::Functional::NoVM_DCBx_SwitchPort_LLDP", "params": {}}, {"params_display": {}, "name": "Networking::Functional::NoVM_ControlPathCommand", "params": {}}, {"params_display": {}, "name": "Networking::Stress::NoVM_DeviceStateWithGracefulRemove", "params": {}}, {"params_display": {}, "name": "Networking::Functional::NoVM_IPv4Ping", "params": {}}, {"params_display": {}, "name": "Networking::Functional::NoVM_NicAdapterInfo", "params": {}}, {"params_display": {}, "name": "Networking::Functional::NoVM_NicMgmtTest", "params": {}}, {"params_display": {}, "name": "Networking::Functional::NoVM_HardwareCheck", "params": {}}, {"params_display": {}, "name": "Networking::Functional::NoVM_Multicast", "params": {}}, {"params_display": {}, "name": "Networking::Functional::NoVM_DeviceStateDriverReload", "params": {}}, {"params_display": {}, "name": "Networking::Functional::NoVM_NegativeParallelOps", "params": {}}, {"params_display": {}, "name": "Networking::Functional::NoVM_DCBx_Check", "params": {}}, {"params_display": {}, "name": "Networking::Geneve::Stress::Mix_Various_Traffic", "params": {}}, {"params_display": {}, "name": "Networking::VXLAN::Stress::Mix_Various_Traffic", "params": {}}, {"params_display": {}, "name": "Networking::SRIOV::Functional::VF_PF", "params": {}}, {"params_display": {}, "name": "Networking::SRIOV::Functional::Disconnect_VF", "params": {}}, {"params_display": {}, "name": "Networking::Geneve::Functional::Offload_IPv4", "params": {}}, {"params_display": {}, "name": "Networking::Geneve::Functional::Offload_IPv6", "params": {}}, {"params_display": {}, "name": "Networking::Geneve::Functional::AddDeleteUplink", "params": {}}, {"params_display": {}, "name": "Networking::Geneve::Functional::ChangeMTU", "params": {}}, {"params_display": {}, "name": "Networking::Geneve::Functional::VMIPMAC_Change", "params": {}}, {"params_display": {}, "name": "Networking::Geneve::Functional::Uplink_Operate", "params": {}}, {"params_display": {}, "name": "Networking::Geneve::Functional::RXFilter", "params": {}}, {"params_display": {}, "name": "Networking::Geneve::Functional::Teaming", "params": {}}, {"params_display": {}, "name": "Networking::Geneve::Functional::Basic_DownUp", "params": {}}, {"params_display": {}, "name": "Networking::Functional::VMOTION", "params": {}}, {"params_display": {}, "name": "Networking::SRIOV::Functional::VGT", "params": {}}, {"params_display": {}, "name": "Networking::SRIOV::Functional::VF_VF", "params": {}}, {"params_display": {}, "name": "Networking::SRIOV::Functional::VF_Reset", "params": {}}, {"params_display": {}, "name": "Networking::SRIOV::Functional::Promiscuous_VF", "params": {}}, {"params_display": {}, "name": "Networking::SRIOV::Functional::VMIPMAC_Change", "params": {}}, {"params_display": {}, "name": "Networking::SRIOV::Functional::Reboot", "params": {}}, {"params_display": {}, "name": "Networking::Stress::RandomOperationIO", "params": {}}, {"params_display": {}, "name": "Networking::Stress::Reboot", "params": {}}, {"params_display": {}, "name": "Networking::Functional::VMOPs", "params": {}}, {"params_display": {}, "name": "Networking::VXLAN::Functional::Basic_DownUp", "params": {}}, {"params_display": {}, "name": "Networking::VXLAN::Functional::ChangeMTU", "params": {}}, {"params_display": {}, "name": "Networking::VXLAN::Functional::RXFilter", "params": {}}, {"params_display": {}, "name": "Networking::VXLAN::Functional::Uplink_Operate", "params": {}}, {"params_display": {}, "name": "Networking::VXLAN::Functional::Offload_IPv4", "params": {}}, {"params_display": {}, "name": "Networking::VXLAN::Functional::Offload_IPv6", "params": {}}, {"params_display": {}, "name": "Networking::VXLAN::Functional::AddDeleteUplink", "params": {}}, {"params_display": {}, "name": "Networking::VXLAN::Functional::Teaming", "params": {}}, {"params_display": {}, "name": "Networking::VXLAN::Functional::VMIPMAC_Change", "params": {}}, {"params_display": {}, "name": "Networking::Functional::VMK_NFSReadWrite", "params": {}}, {"params_display": {}, "name": "Networking::Functional::VMK_TrafficIPv4", "params": {}}, {"params_display": {}, "name": "Networking::Functional::Netqueue", "params": {}}, {"params_display": {}, "name": "Networking::VXLAN::Functional::MixTraffic_Vxlan_Normal", "params": {}}, {"params_display": {}, "name": "Networking::Functional::IPv6::Checksum.Offload", "params": {}}, {"params_display": {}, "name": "Networking::Functional::IPv6::Esxcli_DownUp", "params": {}}, {"params_display": {}, "name": "Networking::Functional::IPv6::FloodPing", "params": {}}, {"params_display": {}, "name": "Networking::Functional::IPv6::VLAN_RX", "params": {}}, {"params_display": {}, "name": "Networking::Functional::IPv6::VLAN_TX", "params": {}}, {"params_display": {}, "name": "Networking::Functional::IPv6::Iozone", "params": {}}, {"params_display": {}, "name": "Networking::Functional::IPv6::MultiCast", "params": {}}, {"params_display": {}, "name": "Networking::Functional::IPv6::Netperf", "params": {}}, {"params_display": {}, "name": "Networking::Functional::IPv6::NicTeaming", "params": {}}, {"params_display": {}, "name": "Networking::Functional::IPv6::SCP", "params": {}}, {"params_display": {}, "name": "Networking::Functional::IPv6::TSO", "params": {}}, {"params_display": {}, "name": "Networking::Stress::IPv6::RandomOperationIO", "params": {}}, {"params_display": {}, "name": "Networking::Stress::IPv6::Reboot", "params": {}}, {"params_display": {}, "name": "Networking::Functional::IPv4VGT", "params": {}}, {"params_display": {}, "name": "Networking::Functional::IPv6VGT", "params": {}}, {"params_display": {}, "name": "Networking::Functional::DynamicNetqueue", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ChangeMTU", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ChangeMTU_Reset", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ChangeMTU_Unload", "params": {}}, {"params_display": {}, "name": "Networking::Functional::Esxcli_DownUp", "params": {}}, {"params_display": {}, "name": "Networking::Functional::FloodPing", "params": {}}, {"params_display": {}, "name": "Networking::Functional::NetDump", "params": {}}, {"params_display": {}, "name": "Networking::Performance::RX_TX_MBPS", "params": {}}, {"params_display": {}, "name": "Networking::Functional::<PERSON><PERSON><PERSON>", "params": {}}, {"params_display": {}, "name": "Networking::Functional::Broadcast", "params": {}}, {"params_display": {}, "name": "Networking::Functional::Iozone", "params": {}}, {"params_display": {}, "name": "Networking::Functional::BasicFailover", "params": {}}, {"params_display": {}, "name": "Networking::Functional::BeaconFailover", "params": {}}, {"params_display": {}, "name": "Networking::Functional::SCP", "params": {}}, {"params_display": {}, "name": "Networking::Functional::ENS::Interrupt::IPv6::LRO", "params": {}}, {"params_display": {}, "name": "Networking::Geneve::Functional::NoVM_EncapRxFilterTwoTuple", "params": {}}, {"params_display": {}, "name": "Networking::Geneve::Functional::RXFilterTwoTuple", "params": {}}]}, "postcert": {"checked": false, "cmd": null, "dependsOn": null, "group": null, "id": null, "name": null, "requiredOnce": false, "skippableIfLastPass": false, "visible": false, "testType": null, "estimatedTime": 0}, "precert": {"checked": false, "cmd": null, "dependsOn": null, "group": null, "id": null, "name": null, "requiredOnce": false, "skippableIfLastPass": false, "visible": false, "testType": null, "estimatedTime": 0}, "selectedTestList": [{"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/TestConfiguration.py Test::Configuration", "dependsOn": null, "group": "Configuration", "id": "e2631284-da49-46f0-9bfd-29e0e6ab38f6", "name": "Test::Configuration", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 3}, {"checked": true, "cmd": "/certs/common/Launcher80/driverlauncher.sh /certs/common/Launcher80/DriverUpgradeLauncher.py vLCM-Upgrade", "dependsOn": null, "group": "Driver-Upgrade", "id": "6e1efd07-d47f-4f04-9e9f-51ca3b12f5dc", "name": "vLCM-ISO-Upgrade", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 30}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/ParamsLauncher.py IUVT::Test::Parameters", "dependsOn": null, "group": "IUVT-Parameters", "id": "c3dc0c0c-b271-4cdd-8d3a-b1bab638b335", "name": "IUVT::Test::Parameters", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 3}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Asyncdriver::PreCheck_Component,AsyncDriver-HPTC", "dependsOn": ["IUVT::Test::Parameters"], "group": "IUVT-Component", "id": "a5f6c40d-86f1-43c6-b150-fc443e5c2b13", "name": "Asyncdriver::PreCheck_Component", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 30}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Asyncdriver::Install_Component,AsyncDriver-HPTC", "dependsOn": ["IUVT::Test::Parameters", "Networking::Functional::NoVM_NicAdapterInfo"], "group": "IUVT-Component", "id": "9b251ba9-a199-4a7a-a66e-fce6015315b4", "name": "Asyncdriver::Install_Component", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 30}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Asyncdriver::Functional::Vib_VersionCheck,AsyncDriver-HPTC", "dependsOn": ["IUVT::Test::Parameters", "Networking::Functional::NoVM_NicAdapterInfo"], "group": "IUVT-Component", "id": "121cb2af-7f32-4571-8e3b-ca63382d3564", "name": "Asyncdriver::Functional::Vib_VersionCheck", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 30}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Asyncdriver::Functional::UEFISecureboot_check,AsyncDriver-HPTC", "dependsOn": ["IUVT::Test::Parameters", "Networking::Functional::NoVM_NicAdapterInfo"], "group": "IUVT-Component", "id": "66571d2c-5210-4a21-9fbc-ffe0d32b2029", "name": "Asyncdriver::Functional::UEFISecureboot_check", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 30}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Asyncdriver::Functional::Api_complaint", "dependsOn": ["IUVT::Test::Parameters", "Networking::Functional::NoVM_NicAdapterInfo"], "group": "IUVT-Component", "id": "76a1c1df-597d-4e7c-8ed2-d1278528446e", "name": "Asyncdriver::Functional::Api_complaint", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 10}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Asyncdriver::Functional::Stacksizecheck", "dependsOn": ["IUVT::Test::Parameters", "Networking::Functional::NoVM_NicAdapterInfo"], "group": "IUVT-Component", "id": "ab67a7af-0fe5-4782-a64b-247257973e3b", "name": "Asyncdriver::Functional::Stacksizecheck", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 10}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/ParamsLauncher.py Test::Parameters", "dependsOn": null, "group": "Parameters", "id": "cd554cec-abf6-4857-bf70-4153cc6993fe", "name": "Test::Parameters", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 3}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ChangeMTU,Networking-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VS_Function", "id": "3294444b-5fb9-47af-836c-7700ec0acc2e", "name": "Networking::Functional::ChangeMTU", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ChangeMTU_Reset,Networking-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VS_Function", "id": "b6064972-e054-494e-a75e-bcedbb2e62dc", "name": "Networking::Functional::ChangeMTU_Reset", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ChangeMTU_Unload,Networking-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VS_Function", "id": "33167418-aba4-45b2-b704-08a11be92d07", "name": "Networking::Functional::ChangeMTU_Unload", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::Esxcli_DownUp,Networking-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VS_Function", "id": "b532d9a4-4b76-4d75-af39-2fc557b060bb", "name": "Networking::Functional::Esxcli_DownUp", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::FloodPing,Networking-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VS_Function", "id": "8b0b6387-577b-470e-b74f-949b081a42e1", "name": "Networking::Functional::FloodPing", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/netdumpcommonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::NetDump,Networking-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VS_Function", "id": "f5b4e8ce-8cbe-4859-b68b-dedc92265367", "name": "Networking::Functional::NetDump", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Performance::RX_TX_MBPS,Networking-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VS_Function", "id": "b269d528-6157-415c-96b2-5b75511990f7", "name": "Networking::Performance::RX_TX_MBPS", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::Blaster,Networking-LPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VS_Function", "id": "ca1b6e3b-2e72-4993-be0c-3780b9e95d15", "name": "Networking::Functional::<PERSON><PERSON><PERSON>", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::Broadcast,Networking-LPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VS_Function", "id": "57a1b485-b664-4bb1-9244-314f407d13c3", "name": "Networking::Functional::Broadcast", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::Iozone,Networking-LPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VS_Function", "id": "020c76bb-4adf-425a-a691-608e70495078", "name": "Networking::Functional::Iozone", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::SCP,Networking-LPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VS_Function", "id": "9ebb4f18-9a91-41da-aa22-2f3454a8ccbd", "name": "Networking::Functional::SCP", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::DynamicNetqueue,Networking-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VS_DynNetQ", "id": "424c4238-d19c-4eea-85b6-05f21c69f222", "name": "Networking::Functional::DynamicNetqueue", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::IPv4VGT,Networking-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VGT_Function", "id": "a3b4924e-132b-45a6-9280-8ff18bc0fbfe", "name": "Networking::Functional::IPv4VGT", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::IPv6VGT,Networking-LPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VGT_Function", "id": "a4d1ea94-937c-45cd-807b-e58c5654a662", "name": "Networking::Functional::IPv6VGT", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::IPv6::Checksum.Offload,Networking-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VS_Function_IPv6", "id": "e6f20204-ec0a-47d8-a717-3c0241b5ebfb", "name": "Networking::Functional::IPv6::Checksum.Offload", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::IPv6::Esxcli_DownUp,Networking-LPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VS_Function_IPv6", "id": "2de22aef-c7c8-4cf9-85ca-578e9749133b", "name": "Networking::Functional::IPv6::Esxcli_DownUp", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::IPv6::FloodPing,Networking-LPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VS_Function_IPv6", "id": "e31494ce-bf6b-47a7-b3e7-e49af7a98586", "name": "Networking::Functional::IPv6::FloodPing", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::IPv6::VLAN_RX,Networking-LPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VS_Function_IPv6", "id": "9cf4c1cb-5b36-4159-9222-126dc562e4c8", "name": "Networking::Functional::IPv6::VLAN_RX", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::IPv6::VLAN_TX,Networking-LPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VS_Function_IPv6", "id": "3d37eb0c-6d57-422c-a13a-eda5b500e886", "name": "Networking::Functional::IPv6::VLAN_TX", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::IPv6::Iozone,Networking-LPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VS_Function_IPv6", "id": "c77f74ae-f27c-4990-9d6a-3f0157a6d9f1", "name": "Networking::Functional::IPv6::Iozone", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::IPv6::MultiCast,Networking-LPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VS_Function_IPv6", "id": "408b5f32-9f68-482f-820f-0a65187dc141", "name": "Networking::Functional::IPv6::MultiCast", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::IPv6::Netperf,Networking-LPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VS_Function_IPv6", "id": "217f729f-8ee6-40d2-a519-b8876a383cf5", "name": "Networking::Functional::IPv6::Netperf", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::IPv6::SCP,Networking-LPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VS_Function_IPv6", "id": "2f46c220-9b34-465e-9e4d-9f88f42141d3", "name": "Networking::Functional::IPv6::SCP", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::IPv6::TSO,Networking-LPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VS_Function_IPv6", "id": "5d5c0201-2d62-4dd8-b55d-b1b7872b9657", "name": "Networking::Functional::IPv6::TSO", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Stress::IPv6::RandomOperationIO,Networking-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VS_Stress_IPv6", "id": "35192886-a1f1-44ca-a1c3-8b72dd4e7f48", "name": "Networking::Stress::IPv6::RandomOperationIO", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Stress::IPv6::Reboot,Networking-LPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VS_Stress_IPv6", "id": "cf2b9570-fe81-4fd2-af87-e8fd6454298d", "name": "Networking::Stress::IPv6::Reboot", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::VXLAN::Functional::MixTraffic_Vxlan_Normal,Networking-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VXLAN_MixEnv", "id": "43c91842-0e5a-4f41-b4ac-054acfa145ae", "name": "Networking::VXLAN::Functional::MixTraffic_Vxlan_Normal", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::Netqueue,Networking-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VS_NetQ", "id": "e47b6fc0-fe90-44e1-9f2b-c935c2d653e0", "name": "Networking::Functional::Netqueue", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::VMK_NFSReadWrite,Networking-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMKNIC", "id": "f3ca1e1c-a700-4b5d-87b6-5ab2fa11af79", "name": "Networking::Functional::VMK_NFSReadWrite", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::VMK_TrafficIPv4,Networking-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMKNIC", "id": "ddd0fd61-c479-483b-92f8-6a01b34fdd58", "name": "Networking::Functional::VMK_TrafficIPv4", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::VXLAN::Functional::ChangeMTU,Networking-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VXLAN_Function", "id": "16956484-df98-4a9f-9880-c4ab03a78e57", "name": "Networking::VXLAN::Functional::ChangeMTU", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::VXLAN::Functional::RXFilter,Networking-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VXLAN_Function", "id": "e02a3b84-f0f6-4903-8653-888c53ff70c5", "name": "Networking::VXLAN::Functional::RXFilter", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::VXLAN::Functional::Uplink_Operate,Networking-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VXLAN_Function", "id": "febd9f62-12cd-42aa-b581-19d5d267ade3", "name": "Networking::VXLAN::Functional::Uplink_Operate", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::VXLAN::Functional::Offload_IPv4,Networking-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VXLAN_Function", "id": "3d5c2418-f51c-44fa-bbe3-0ac3ab7a906a", "name": "Networking::VXLAN::Functional::Offload_IPv4", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::VXLAN::Functional::Offload_IPv6,Networking-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VXLAN_Function", "id": "d9667389-9109-4ee6-ac2c-a0ccba1d144a", "name": "Networking::VXLAN::Functional::Offload_IPv6", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::VXLAN::Functional::AddDeleteUplink,Networking-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VXLAN_Function", "id": "f332d2b9-bc9d-4e86-b8f2-26cc65169b1d", "name": "Networking::VXLAN::Functional::AddDeleteUplink", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::VXLAN::Functional::VMIPMAC_Change,Networking-LPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VXLAN_Function", "id": "90a64004-d054-4555-a250-f35623bba646", "name": "Networking::VXLAN::Functional::VMIPMAC_Change", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Stress::RandomOperationIO,Networking-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VS_Stress", "id": "d4294cf6-09c4-4a87-a60e-3713f1828f7d", "name": "Networking::Stress::RandomOperationIO", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Stress::Reboot,Networking-LPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VS_Stress", "id": "e48e5f88-ad52-4dcf-af36-a9f7ed22a6c4", "name": "Networking::Stress::Reboot", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::SRIOV::Functional::VF_VF,Networking-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VS_Function_SRIOV_ETH1", "id": "4b2660d8-c942-45df-914d-6922a68b23cc", "name": "Networking::SRIOV::Functional::VF_VF", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::SRIOV::Functional::VF_Reset,Networking-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VS_Function_SRIOV_ETH1", "id": "fa29b216-4f32-4525-8f47-66c5031201be", "name": "Networking::SRIOV::Functional::VF_Reset", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::SRIOV::Functional::Promiscuous_VF,Networking-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VS_Function_SRIOV_ETH1", "id": "a8adf1e9-b225-4d81-ad84-1ca88efff1b8", "name": "Networking::SRIOV::Functional::Promiscuous_VF", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::SRIOV::Functional::VMIPMAC_Change,Networking-LPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VS_Function_SRIOV_ETH1", "id": "db77dc69-0863-4cea-9d45-085e67d38b4d", "name": "Networking::SRIOV::Functional::VMIPMAC_Change", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::SRIOV::Functional::Reboot,Networking-LPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VS_Function_SRIOV_ETH1", "id": "52ac8c25-3438-4994-9e65-12d2355a5315", "name": "Networking::SRIOV::Functional::Reboot", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::SRIOV::Functional::VGT,Networking-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_MIX_SRIOV_VGT", "id": "5d687f58-6b85-4cdb-9023-ac05556da7e8", "name": "Networking::SRIOV::Functional::VGT", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Geneve::Functional::Offload_IPv4,Networking-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_Geneve_Function_VM", "id": "b6b873e5-d60a-4568-b9eb-00e136b3f4a0", "name": "Networking::Geneve::Functional::Offload_IPv4", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Geneve::Functional::Offload_IPv6,Networking-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_Geneve_Function_VM", "id": "ba648cbf-4c5d-45e4-8cf9-4483ab630718", "name": "Networking::Geneve::Functional::Offload_IPv6", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Geneve::Functional::AddDeleteUplink,Networking-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_Geneve_Function_VM", "id": "842ce79f-747c-47ca-9911-b9731f349ae0", "name": "Networking::Geneve::Functional::AddDeleteUplink", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Geneve::Functional::ChangeMTU,Networking-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_Geneve_Function_VM", "id": "ef163138-9df7-47cd-98e5-dc1af0ad2a7e", "name": "Networking::Geneve::Functional::ChangeMTU", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Geneve::Functional::VMIPMAC_Change,Networking-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_Geneve_Function_VM", "id": "b269985a-c09b-42e7-84f5-08db5999580a", "name": "Networking::Geneve::Functional::VMIPMAC_Change", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Geneve::Functional::Uplink_Operate,Networking-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_Geneve_Function_VM", "id": "d7b09da4-5e0e-4c2f-a542-e334c5dddf38", "name": "Networking::Geneve::Functional::Uplink_Operate", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Geneve::Functional::RXFilter,Networking-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_Geneve_Function_VM", "id": "b34da644-367f-4c52-bf13-0bee17954fcc", "name": "Networking::Geneve::Functional::RXFilter", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::SRIOV::Functional::VF_PF,Networking-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_MIX_SRIOV", "id": "70546794-43ad-44fd-a79e-b5e65a5b274c", "name": "Networking::SRIOV::Functional::VF_PF", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::VXLAN::Stress::Mix_Various_Traffic,Networking-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_Stress_VXLAN_MixEnv", "id": "cd1efe0f-b172-411c-ae59-72251328c9fd", "name": "Networking::VXLAN::Stress::Mix_Various_Traffic", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Geneve::Stress::Mix_Various_Traffic,Networking-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_Stress_Geneve_MixEnv", "id": "77e112db-d612-40ee-8b70-d928cfef0804", "name": "Networking::Geneve::Stress::Mix_Various_Traffic", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::NoVM_DefaultqRSS,Networking_NoVM-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK", "id": "b9ea72a1-567f-441d-ac76-2f33016c6072", "name": "Networking::Functional::NoVM_DefaultqRSS", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::NoVM_DeviceRSS,Networking_NoVM-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK", "id": "770f7c59-5573-414a-8460-924856cbf2bf", "name": "Networking::Functional::NoVM_DeviceRSS", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::NoVM_DriverLoadUnLoad,Networking_NoVM-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK", "id": "4b7a6051-e437-451a-9ecd-83d15ba10a50", "name": "Networking::Functional::NoVM_DriverLoadUnLoad", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::NoVM_VLAN_TX,Networking_NoVM-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK", "id": "2b628aa4-97e8-4262-b5ad-e6b873328122", "name": "Networking::Functional::NoVM_VLAN_TX", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::NoVM_HW_Cap,Networking_NoVM-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK", "id": "92a66a5e-3264-42eb-abb1-145766c28155", "name": "Networking::Functional::NoVM_HW_Cap", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::NoVM_IPChecksumOffload,Networking_NoVM-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK", "id": "97811891-7430-43e1-b637-013e60fec876", "name": "Networking::Functional::NoVM_IPChecksumOffload", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::NoVM_IPv4SCP,Networking_NoVM-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK", "id": "2d8d4c4b-e0dc-4f40-a59c-0ba82c5af6e5", "name": "Networking::Functional::NoVM_IPv4SCP", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::NoVM_IPv4VlanTag,Networking_NoVM-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK", "id": "c398e2a8-1811-4763-8157-4c965ceb2381", "name": "Networking::Functional::NoVM_IPv4VlanTag", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::NoVM_IPv4JF,Networking_NoVM-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK", "id": "1ac99091-1ece-4c96-be70-beae66a99cc3", "name": "Networking::Functional::NoVM_IPv4JF", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::NoVM_RSS,Networking_NoVM-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK", "id": "8a7d18fa-0f83-4e01-b8fa-de94d1d8c62a", "name": "Networking::Functional::NoVM_RSS", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::NoVM_IPv4Netperf,Networking_NoVM-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK", "id": "356ff198-d3c5-4e1f-8776-cbee71de8fe0", "name": "Networking::Functional::NoVM_IPv4Netperf", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::NoVM_DriverReloadWithParams,Networking_NoVM-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK", "id": "033ab353-0e03-40c9-ac55-c706f25dd471", "name": "Networking::Functional::NoVM_DriverReloadWithParams", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::NoVM_DriverReloadWithSpecParams,Networking_NoVM-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK", "id": "c2fe9dcb-9dad-49d6-ac42-e9f3d580e4d6", "name": "Networking::Functional::NoVM_DriverReloadWithSpecParams", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::NoVM_IPv4TSO,Networking_NoVM-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK", "id": "af95ce46-6a4b-472e-90ac-7e0086abfcde", "name": "Networking::Functional::NoVM_IPv4TSO", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::NoVM_DeviceStateChange,Networking_NoVM-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK", "id": "600ac6b3-128b-4cc6-80f8-bd81594323d5", "name": "Networking::Functional::NoVM_DeviceStateChange", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::NoVM_IPv4RandomPktInject,Networking_NoVM-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK", "id": "b020f9ec-20fc-4af5-ac73-6242654360fb", "name": "Networking::Functional::NoVM_IPv4RandomPktInject", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::NoVM_LoadEsx_Reboot,Networking_NoVM-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK", "id": "55ae660a-2299-43e1-ac03-ac7b2cf00674", "name": "Networking::Functional::NoVM_LoadEsx_Reboot", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::NoVM_NegativeDeviceStateChange,Networking_NoVM-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK", "id": "2254f02c-c8f2-471e-94d6-5816b33e2895", "name": "Networking::Functional::NoVM_NegativeDeviceStateChange", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::NoVM_MultiRSS,Networking_NoVM-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK", "id": "04d40eca-a33b-4684-a1e1-7846cbe64261", "name": "Networking::Functional::NoVM_MultiRSS", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::NoVM_IPv4GenEncapPktInject,Networking_NoVM-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK", "id": "166e1abf-a12d-4053-b831-e5c2ccde4739", "name": "Networking::Functional::NoVM_IPv4GenEncapPktInject", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::NoVM_ControlPathCommand,Networking_NoVM-LPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK", "id": "fe351823-6b5b-4024-92f0-aa44158f495d", "name": "Networking::Functional::NoVM_ControlPathCommand", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Stress::NoVM_DeviceStateWithGracefulRemove,Networking_NoVM-LPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK", "id": "0a3a9752-3486-4bfd-83d2-c1fe220804ec", "name": "Networking::Stress::NoVM_DeviceStateWithGracefulRemove", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::NoVM_IPv4Ping,Networking_NoVM-LPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK", "id": "2b974ff8-f634-47af-92a7-6f580140e617", "name": "Networking::Functional::NoVM_IPv4Ping", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::NoVM_NicAdapterInfo,Networking_NoVM-LPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK", "id": "26a69194-4646-4e24-82b2-f1184e7d72b2", "name": "Networking::Functional::NoVM_NicAdapterInfo", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::NoVM_NicMgmtTest,Networking_NoVM-LPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK", "id": "dd48ce1c-ce90-45f1-a776-3e1ba0e3c478", "name": "Networking::Functional::NoVM_NicMgmtTest", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::NoVM_HardwareCheck,Networking_NoVM-LPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK", "id": "fbe3147c-fa6c-440e-8539-82ba9b2f30b1", "name": "Networking::Functional::NoVM_HardwareCheck", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::NoVM_Multicast,Networking_NoVM-LPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK", "id": "7cbdaac4-7a2a-4ab6-a21f-2179cb5786fa", "name": "Networking::Functional::NoVM_Multicast", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::NoVM_DeviceStateDriverReload,Networking_NoVM-LPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK", "id": "68f1462e-b0ac-4e5f-9a12-6a0083b5786f", "name": "Networking::Functional::NoVM_DeviceStateDriverReload", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::NoVM_NegativeParallelOps,Networking_NoVM-LPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK", "id": "1bc555bc-b498-40df-b726-ee6160f91d06", "name": "Networking::Functional::NoVM_NegativeParallelOps", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::NoVM_IPv6JF,Networking_NoVM-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_IPv6", "id": "06b5bd47-e5bf-447d-8e9d-93f42c6ba1cf", "name": "Networking::Functional::NoVM_IPv6JF", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::NoVM_IPv6Ping,Networking_NoVM-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_IPv6", "id": "a6f59394-2443-4155-8262-d98f35fbf829", "name": "Networking::Functional::NoVM_IPv6Ping", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::NoVM_IPv6VlanTag,Networking_NoVM-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_IPv6", "id": "8e2b0803-0761-43ee-a1d3-0aa4bf5ce236", "name": "Networking::Functional::NoVM_IPv6VlanTag", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::NoVM_IPv6Netperf,Networking_NoVM-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_IPv6", "id": "1cbda7e7-6ae1-49e0-83fa-eef07151eb0c", "name": "Networking::Functional::NoVM_IPv6Netperf", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::NoVM_IPv6TSO,Networking_NoVM-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_IPv6", "id": "1a496212-f1a4-4506-97ce-96afd997e453", "name": "Networking::Functional::NoVM_IPv6TSO", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::NoVM_IPv6RandomPktInject,Networking_NoVM-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_IPv6", "id": "99c25844-88c3-434f-ac84-66a5cb8db899", "name": "Networking::Functional::NoVM_IPv6RandomPktInject", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::NoVM_IPv6SCP,Networking_NoVM-LPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_IPv6", "id": "1182d7cf-c230-45a7-9ecd-095c073bb9ae", "name": "Networking::Functional::NoVM_IPv6SCP", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Geneve::Functional::NoVM_ChangeUdpPort,Networking_NoVM-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_Geneve_Function", "id": "43f5db81-3d79-4bd9-b1f7-924ab77b4c1e", "name": "Networking::Geneve::Functional::NoVM_ChangeUdpPort", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Geneve::Functional::NoVM_Option,Networking_NoVM-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_Geneve_Function", "id": "5dfbc8b0-73b6-4d91-a102-47cfd212593a", "name": "Networking::Geneve::Functional::NoVM_Option", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::NoVM_SameMac_Filter,Networking_NoVM-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_SameMac", "id": "31aeef17-653f-4992-b52f-044cb65709aa", "name": "Networking::Functional::NoVM_SameMac_Filter", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::IPv6::Netqueue,Networking-LPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VS_NetQ_IPv6", "id": "c91de7bd-d401-41d8-9c26-42829da2f312", "name": "Networking::Functional::IPv6::Netqueue", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::VXLAN::Functional::IPv4VGT,Networking-LPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VXLAN_VGT", "id": "463baf6b-0636-4180-80f2-6dd78d49f05b", "name": "Networking::VXLAN::Functional::IPv4VGT", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::VXLAN::Stress::RandomOperationIO,Networking-LPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VXLAN_Stress", "id": "83667fef-c1f1-4566-b569-225720574e54", "name": "Networking::VXLAN::Stress::RandomOperationIO", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::VXLAN::Stress::Traffic,Networking-LPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VXLAN_Stress", "id": "68669ff5-55b6-4a7e-89c0-46c2fbecf4e2", "name": "Networking::VXLAN::Stress::Traffic", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Geneve::Stress::Traffic,Networking-LPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_Geneve_Stress", "id": "02e2b1bd-fc01-4f8f-8203-9f14f6aa04ec", "name": "Networking::Geneve::Stress::Traffic", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Geneve::Stress::RandomOperationIO,Networking-LPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_Geneve_Stress", "id": "3584c1c9-18de-459a-8df1-c3758ce8ca73", "name": "Networking::Geneve::Stress::RandomOperationIO", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::SRIOV::Stress::RandomOperationIO,Networking-LPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VS_Stress_SRIOV_ETH1", "id": "f136d870-7843-42a6-931d-2e6079a835c0", "name": "Networking::SRIOV::Stress::RandomOperationIO", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Geneve::Functional::IPv4VGT,Networking-LPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_Geneve_VGT", "id": "410c2408-1684-40e6-86e3-fcfa57bf1111", "name": "Networking::Geneve::Functional::IPv4VGT", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::SRIOV::Functional::PrimaryPFDeviceState,Networking-LPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_MIX_SRIOV_PrimaryNonPrimary", "id": "43d7d6f6-607f-469f-bb9a-6ca351e6e7e6", "name": "Networking::SRIOV::Functional::PrimaryPFDeviceState", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::SRIOV::Functional::NoVM_DeviceStateChange,Networking_NoVM-LPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_SRIOV", "id": "b12c1032-bbf5-4df0-a274-0753830e1694", "name": "Networking::SRIOV::Functional::NoVM_DeviceStateChange", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::SRIOV::Functional::NoVM_NegativeDeviceStateChange,Networking_NoVM-LPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_SRIOV", "id": "1dc2146a-592d-429e-9c08-b8c026c181d8", "name": "Networking::SRIOV::Functional::NoVM_NegativeDeviceStateChange", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::SRIOV::Functional::NoVM_PF_IPv4TSO,Networking_NoVM-LPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_SRIOV", "id": "c99e0ae3-8afc-485d-815b-d7281379aa83", "name": "Networking::SRIOV::Functional::NoVM_PF_IPv4TSO", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Polling::NoVM_DeviceStateChange,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_ENS_Polling", "id": "7168fd7a-0c59-4b35-81ba-3d0d6f49f494", "name": "Networking::Functional::ENS::Polling::NoVM_DeviceStateChange", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Polling::NoVM_VLAN_RX,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_ENS_Polling", "id": "4ed7c2f4-31cc-47ec-ae0c-ef1badf64e30", "name": "Networking::Functional::ENS::Polling::NoVM_VLAN_RX", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Polling::NoVM_DeviceStateDriverReload,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_ENS_Polling", "id": "b03d863a-ce60-4094-a34e-87bda31f4792", "name": "Networking::Functional::ENS::Polling::NoVM_DeviceStateDriverReload", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Polling::NoVM_IPv4TSO,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_ENS_Polling", "id": "b0b5f269-23b6-49c6-8faf-0ecf985b438b", "name": "Networking::Functional::ENS::Polling::NoVM_IPv4TSO", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Polling::NoVM_ControlPathCommand,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_ENS_Polling", "id": "562a6b3c-bd1c-4861-a009-318f90781c07", "name": "Networking::Functional::ENS::Polling::NoVM_ControlPathCommand", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Polling::NoVM_NegativeDeviceStateChange,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_ENS_Polling", "id": "ac1f16c8-ed6c-49f2-9637-9bde2fcb47ac", "name": "Networking::Functional::ENS::Polling::NoVM_NegativeDeviceStateChange", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Polling::NoVM_DriverLoadUnLoad,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_ENS_Polling", "id": "fb07cdd2-495a-46f3-85a8-ca593f731125", "name": "Networking::Functional::ENS::Polling::NoVM_DriverLoadUnLoad", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Polling::NoVM_IPv4Ping,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_ENS_Polling", "id": "577464c3-7499-4335-a0f5-c70653b2f02b", "name": "Networking::Functional::ENS::Polling::NoVM_IPv4Ping", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Polling::NoVM_IPv4JF,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_ENS_Polling", "id": "626e9de7-7d69-49a2-890a-b0e4716080ee", "name": "Networking::Functional::ENS::Polling::NoVM_IPv4JF", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Polling::NoVM_IPv4Netperf,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_ENS_Polling", "id": "d02ad9dc-fa52-4f49-bff1-e99497db580c", "name": "Networking::Functional::ENS::Polling::NoVM_IPv4Netperf", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Polling::NoVM_IPv4SCP,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_ENS_Polling", "id": "ee4760fe-e48c-4ac5-8dc6-3e9d442ec2b2", "name": "Networking::Functional::ENS::Polling::NoVM_IPv4SCP", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Polling::NoVM_VLAN_TX,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_ENS_Polling", "id": "fe750015-32d4-4ee4-87bc-6e25b8ff01d4", "name": "Networking::Functional::ENS::Polling::NoVM_VLAN_TX", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Polling::NoVM_DriverReloadWithParams,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_ENS_Polling", "id": "1c23e46a-70b6-44c3-a891-eac86903064b", "name": "Networking::Functional::ENS::Polling::NoVM_DriverReloadWithParams", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Polling::NoVM_DriverReloadWithSpecParams,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_ENS_Polling", "id": "2810694b-c6af-4360-888d-f52e945b6113", "name": "Networking::Functional::ENS::Polling::NoVM_DriverReloadWithSpecParams", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Polling::NoVM_Multicast,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_ENS_Polling", "id": "3d1eb025-f39c-4717-9c7e-b1787d243489", "name": "Networking::Functional::ENS::Polling::NoVM_Multicast", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Polling::NoVM_LoadEsx_Reboot,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_ENS_Polling", "id": "fb5bbbf7-804f-461e-ae55-bb3bff1c931d", "name": "Networking::Functional::ENS::Polling::NoVM_LoadEsx_Reboot", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Polling::NoVM_HardwareCheck,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_ENS_Polling", "id": "17774901-3445-4cf0-826d-90e7463bddee", "name": "Networking::Functional::ENS::Polling::NoVM_HardwareCheck", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Polling::NoVM_NegativeParallelOps,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_ENS_Polling", "id": "2b509bea-5744-4fca-b493-213fa12f6b70", "name": "Networking::Functional::ENS::Polling::NoVM_NegativeParallelOps", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Polling::NoVM_IPv4VlanTag,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_ENS_Polling", "id": "b7f07186-3711-4f53-817e-3ba8a11e09e1", "name": "Networking::Functional::ENS::Polling::NoVM_IPv4VlanTag", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Stress::ENS::Polling::NoVM_DeviceStateWithGracefulRemove,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_ENS_Polling", "id": "2bb21601-f28e-4a0c-b24f-ede76576db3a", "name": "Networking::Stress::ENS::Polling::NoVM_DeviceStateWithGracefulRemove", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Polling::NoVM_DefaultqRSS,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_ENS_Polling", "id": "ad7bdbda-dea4-40f7-a047-b33acadfafd5", "name": "Networking::Functional::ENS::Polling::NoVM_DefaultqRSS", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Polling::NoVM_IPv6SCP,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_ENS_Polling_IPv6", "id": "9c2879e0-c807-4555-998a-6407781681b8", "name": "Networking::Functional::ENS::Polling::NoVM_IPv6SCP", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Polling::NoVM_IPv6JF,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_ENS_Polling_IPv6", "id": "f257804c-a96a-4587-a764-0c7b0359c429", "name": "Networking::Functional::ENS::Polling::NoVM_IPv6JF", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Polling::NoVM_IPv6Ping,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_ENS_Polling_IPv6", "id": "678fea07-bfec-4f48-a41f-f19d202274a8", "name": "Networking::Functional::ENS::Polling::NoVM_IPv6Ping", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Polling::NoVM_IPv6Netperf,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_ENS_Polling_IPv6", "id": "0f29b6e0-71e7-4ac7-a1e9-193e6d09835b", "name": "Networking::Functional::ENS::Polling::NoVM_IPv6Netperf", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Polling::NoVM_IPv6TSO,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_ENS_Polling_IPv6", "id": "d2a7113e-2ab4-4d1c-a340-eecf077b5ed8", "name": "Networking::Functional::ENS::Polling::NoVM_IPv6TSO", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Polling::NoVM_IPv6VlanTag,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_ENS_Polling_IPv6", "id": "0f44209c-94e5-4887-9a7d-e335f774b343", "name": "Networking::Functional::ENS::Polling::NoVM_IPv6VlanTag", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Geneve::ENS::Functional::Polling::ChangeMTU,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_Geneve_Function_ENS_Polling", "id": "add12d33-b4fe-4b0a-8296-536fc1a440b4", "name": "Networking::Geneve::ENS::Functional::Polling::ChangeMTU", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Geneve::ENS::Functional::Polling::AddDeleteUplink,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_Geneve_Function_ENS_Polling", "id": "6002314f-42b0-496e-bb4c-3f065a4e7491", "name": "Networking::Geneve::ENS::Functional::Polling::AddDeleteUplink", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Geneve::ENS::Functional::Polling::Offload_IPv4,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_Geneve_Function_ENS_Polling", "id": "122e4d6b-5bcc-4ab2-ad4d-11b2954991db", "name": "Networking::Geneve::ENS::Functional::Polling::Offload_IPv4", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Geneve::ENS::Functional::Polling::Offload_IPv6,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_Geneve_Function_ENS_Polling", "id": "821cca97-4364-45ef-9f37-c3ebac8e1e5c", "name": "Networking::Geneve::ENS::Functional::Polling::Offload_IPv6", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Geneve::ENS::Functional::Polling::Uplink_Operate,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_Geneve_Function_ENS_Polling", "id": "222916ba-b3b1-4aef-8b23-f8bf32196909", "name": "Networking::Geneve::ENS::Functional::Polling::Uplink_Operate", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Geneve::ENS::Functional::Polling::VMIPMAC_Change,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_Geneve_Function_ENS_Polling", "id": "df4706a2-16e8-4a3c-8a27-2522c75fc1bb", "name": "Networking::Geneve::ENS::Functional::Polling::VMIPMAC_Change", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Geneve::ENS::Functional::Polling::RXFilter,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_Geneve_Function_ENS_Polling", "id": "90900145-18c7-4714-840a-e5bd19edfae1", "name": "Networking::Geneve::ENS::Functional::Polling::RXFilter", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Geneve::ENS::Stress::Polling::RandomOperationIO,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_Geneve_Stress_ENS_Polling", "id": "1e871e04-ae05-4c60-8acb-6b2bbe8d93b9", "name": "Networking::Geneve::ENS::Stress::Polling::RandomOperationIO", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Geneve::ENS::Stress::Polling::Traffic,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_Geneve_Stress_ENS_Polling", "id": "4667e9c4-dea4-4535-9a37-087b340a73f2", "name": "Networking::Geneve::ENS::Stress::Polling::Traffic", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Polling::IPv4VGT,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VGT_Function_ENS_Polling", "id": "f33170f6-a59d-4566-a60b-d089f25de2d1", "name": "Networking::Functional::ENS::Polling::IPv4VGT", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Polling::IPv6VGT,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VGT_Function_ENS_Polling", "id": "c2c210bf-669d-498e-954c-9814389bb03d", "name": "Networking::Functional::ENS::Polling::IPv6VGT", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Polling::VMK_NFSReadWrite,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMKNIC_ENS_Polling", "id": "6442d181-a58d-4ebc-8dc8-fc32e02de3eb", "name": "Networking::Functional::ENS::Polling::VMK_NFSReadWrite", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Polling::VMK_TrafficIPv4,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMKNIC_ENS_Polling", "id": "ee415643-4c4f-4378-9cec-881723e8987e", "name": "Networking::Functional::ENS::Polling::VMK_TrafficIPv4", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Polling::Iozone,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VDS_Function_ENS_Polling", "id": "9523cb74-3d02-449a-af69-df43ecac278f", "name": "Networking::Functional::ENS::Polling::Iozone", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Polling::FloodPing,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VDS_Function_ENS_Polling", "id": "9995c59c-27ba-40af-81bd-8717c1757d8e", "name": "Networking::Functional::ENS::Polling::FloodPing", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Polling::Blaster,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VDS_Function_ENS_Polling", "id": "7159f944-9563-4202-bb33-bf6262e92afa", "name": "Networking::Functional::ENS::Polling::Blaster", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Polling::Broadcast,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VDS_Function_ENS_Polling", "id": "4fa22aa9-8843-4bd2-abce-3b35ca5c47c4", "name": "Networking::Functional::ENS::Polling::Broadcast", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Polling::ChangeEnsMode,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VDS_Function_ENS_Polling", "id": "3cd0ba20-60e0-44cd-8a92-2b488669760d", "name": "Networking::Functional::ENS::Polling::ChangeEnsMode", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Stress::ENS::Polling::Reboot,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VDS_Stress_ENS_Polling", "id": "6fd2ab81-1317-486c-b041-41f74a344eb9", "name": "Networking::Stress::ENS::Polling::Reboot", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Stress::ENS::Polling::RandomOperationIO,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VDS_Stress_ENS_Polling", "id": "12ed035e-c236-4110-9512-7586176f7b8b", "name": "Networking::Stress::ENS::Polling::RandomOperationIO", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Polling::IPv6::SCP,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VDS_Function_ENS_Polling_IPv6", "id": "bcd34710-af89-4d92-b570-000f0b301576", "name": "Networking::Functional::ENS::Polling::IPv6::SCP", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Polling::IPv6::MultiCast,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VDS_Function_ENS_Polling_IPv6", "id": "4defbd50-ee09-4b1e-8950-35a6e10a0f53", "name": "Networking::Functional::ENS::Polling::IPv6::MultiCast", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Polling::IPv6::VLAN_RX,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VDS_Function_ENS_Polling_IPv6", "id": "5e7bff25-39a8-4025-8129-319665afc3f9", "name": "Networking::Functional::ENS::Polling::IPv6::VLAN_RX", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Polling::IPv6::Iozone,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VDS_Function_ENS_Polling_IPv6", "id": "a6917fe5-d6e0-4305-8c85-fb98d426172f", "name": "Networking::Functional::ENS::Polling::IPv6::Iozone", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Polling::IPv6::FloodPing,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VDS_Function_ENS_Polling_IPv6", "id": "8cfd5dc2-de69-4dc1-846d-59f70f31ef49", "name": "Networking::Functional::ENS::Polling::IPv6::FloodPing", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Polling::IPv6::Checksum.Offload,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VDS_Function_ENS_Polling_IPv6", "id": "10f51282-0941-4f68-90ba-59907afc390a", "name": "Networking::Functional::ENS::Polling::IPv6::Checksum.Offload", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Polling::IPv6::VLAN_TX,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VDS_Function_ENS_Polling_IPv6", "id": "8e5a249f-79c9-4e8d-adc2-3eabb8c914db", "name": "Networking::Functional::ENS::Polling::IPv6::VLAN_TX", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Polling::IPv6::TSO,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VDS_Function_ENS_Polling_IPv6", "id": "99606c40-4479-4b6f-a320-333041785745", "name": "Networking::Functional::ENS::Polling::IPv6::TSO", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Polling::IPv6::Netperf,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VDS_Function_ENS_Polling_IPv6", "id": "fde4f2b5-6167-47ef-bf38-0067c26c0f68", "name": "Networking::Functional::ENS::Polling::IPv6::Netperf", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Geneve::ENS::Stress::Polling::Mix_Various_Traffic,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VDS_MIX_Stress_ENS_Polling", "id": "2e2b5323-2ffd-4f17-b567-43e57b8496f4", "name": "Networking::Geneve::ENS::Stress::Polling::Mix_Various_Traffic", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Geneve::ENS::Functional::Polling::IPv4VGT,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_Geneve_VGT_ENS_Polling", "id": "b3d3df4b-e89f-41a7-9d90-d8f83a8d2294", "name": "Networking::Geneve::ENS::Functional::Polling::IPv4VGT", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Interrupt::NoVM_DefaultqRSS,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_ENS_Interrupt", "id": "0b7f722d-29c0-4f1d-ab44-5d88143b1821", "name": "Networking::Functional::ENS::Interrupt::NoVM_DefaultqRSS", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Interrupt::NoVM_ControlPathCommand,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_ENS_Interrupt", "id": "1126ccc3-cdb5-447e-9b5e-e5fc6de3121f", "name": "Networking::Functional::ENS::Interrupt::NoVM_ControlPathCommand", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Interrupt::NoVM_DeviceStateChange,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_ENS_Interrupt", "id": "dacedd70-6485-4d67-9e89-4d36082aef4f", "name": "Networking::Functional::ENS::Interrupt::NoVM_DeviceStateChange", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Interrupt::NoVM_DeviceStateDriverReload,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_ENS_Interrupt", "id": "a7d29ad5-b1aa-4d2f-92ce-9be230920efd", "name": "Networking::Functional::ENS::Interrupt::NoVM_DeviceStateDriverReload", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Interrupt::NoVM_DriverLoadUnLoad,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_ENS_Interrupt", "id": "9c4c5fa1-febf-4bef-a855-499713596bc6", "name": "Networking::Functional::ENS::Interrupt::NoVM_DriverLoadUnLoad", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Interrupt::NoVM_DriverReloadWithParams,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_ENS_Interrupt", "id": "dcff3d1e-185e-490a-9832-5973840cc6a6", "name": "Networking::Functional::ENS::Interrupt::NoVM_DriverReloadWithParams", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Interrupt::NoVM_DriverReloadWithSpecParams,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_ENS_Interrupt", "id": "e2405a7e-fbf0-4589-a9bc-c4b9f724a5e4", "name": "Networking::Functional::ENS::Interrupt::NoVM_DriverReloadWithSpecParams", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Interrupt::NoVM_HardwareCheck,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_ENS_Interrupt", "id": "96317011-b4bb-416e-a453-6a44eddbdfa0", "name": "Networking::Functional::ENS::Interrupt::NoVM_HardwareCheck", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Interrupt::NoVM_IPv4JF,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_ENS_Interrupt", "id": "2ea19c93-4204-45a5-862d-f5fc0e9261d9", "name": "Networking::Functional::ENS::Interrupt::NoVM_IPv4JF", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Interrupt::NoVM_IPv4Netperf,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_ENS_Interrupt", "id": "8570c42b-021a-4a43-96fc-5b6822f000e2", "name": "Networking::Functional::ENS::Interrupt::NoVM_IPv4Netperf", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Interrupt::NoVM_IPv4Ping,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_ENS_Interrupt", "id": "f0f3d911-6cd5-49c5-bf9b-838fdad12d8b", "name": "Networking::Functional::ENS::Interrupt::NoVM_IPv4Ping", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Interrupt::NoVM_IPv4SCP,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_ENS_Interrupt", "id": "c69864cd-e847-4370-8b1e-f00ab64b2702", "name": "Networking::Functional::ENS::Interrupt::NoVM_IPv4SCP", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Interrupt::NoVM_IPv4TSO,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_ENS_Interrupt", "id": "e8f4c554-24df-43cd-9969-6c59782023ec", "name": "Networking::Functional::ENS::Interrupt::NoVM_IPv4TSO", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Interrupt::NoVM_IPv4VlanTag,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_ENS_Interrupt", "id": "36eff9ca-5ea1-40e6-abf6-75ca0a1fceab", "name": "Networking::Functional::ENS::Interrupt::NoVM_IPv4VlanTag", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Interrupt::NoVM_LoadEsx_Reboot,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_ENS_Interrupt", "id": "3a7deb5a-2af7-4952-909a-6ad79469c06e", "name": "Networking::Functional::ENS::Interrupt::NoVM_LoadEsx_Reboot", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Interrupt::NoVM_Multicast,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_ENS_Interrupt", "id": "c45459ec-3a1f-4a09-8984-0935f06fb4e0", "name": "Networking::Functional::ENS::Interrupt::NoVM_Multicast", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Interrupt::NoVM_NegativeDeviceStateChange,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_ENS_Interrupt", "id": "142a902a-3b30-4167-b755-021ea44150b9", "name": "Networking::Functional::ENS::Interrupt::NoVM_NegativeDeviceStateChange", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Interrupt::NoVM_NegativeParallelOps,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_ENS_Interrupt", "id": "73efbc04-7d61-4dcc-8f55-0b3e6032414e", "name": "Networking::Functional::ENS::Interrupt::NoVM_NegativeParallelOps", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Interrupt::NoVM_VLAN_RX,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_ENS_Interrupt", "id": "19d327f9-04ef-4b72-b1d0-5d8085c3b923", "name": "Networking::Functional::ENS::Interrupt::NoVM_VLAN_RX", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Interrupt::NoVM_VLAN_TX,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_ENS_Interrupt", "id": "3a9318d4-2269-468d-a351-4a5154ac621a", "name": "Networking::Functional::ENS::Interrupt::NoVM_VLAN_TX", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Stress::ENS::Interrupt::NoVM_DeviceStateWithGracefulRemove,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_ENS_Interrupt", "id": "188456f9-5cdb-49fb-88ac-732cabc672c7", "name": "Networking::Stress::ENS::Interrupt::NoVM_DeviceStateWithGracefulRemove", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Interrupt::Blaster,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VDS_Function_ENS_Interrupt", "id": "2424b482-eea0-4fa1-ac27-2db4ac041eeb", "name": "Networking::Functional::ENS::Interrupt::<PERSON><PERSON><PERSON>", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Interrupt::Broadcast,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VDS_Function_ENS_Interrupt", "id": "9848c647-8cc8-4413-8130-9108c24309d0", "name": "Networking::Functional::ENS::Interrupt::Broadcast", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Interrupt::ChangeMTU_Reset,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VDS_Function_ENS_Interrupt", "id": "c7afa2a4-7f20-4283-a9f8-05d3a5cece60", "name": "Networking::Functional::ENS::Interrupt::ChangeMTU_Reset", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Interrupt::FloodPing,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VDS_Function_ENS_Interrupt", "id": "813a68e2-c0b6-449d-94dd-a7a3a182addb", "name": "Networking::Functional::ENS::Interrupt::FloodPing", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Interrupt::Iozone,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VDS_Function_ENS_Interrupt", "id": "f7e3c8df-4302-4e6d-bcc7-9da2f06e1fcb", "name": "Networking::Functional::ENS::Interrupt::Iozone", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Interrupt::Multimode_ChangeMTU_Unload,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VDS_Function_ENS_Interrupt", "id": "d81fee3e-17e5-42e4-9638-58fab8553ac4", "name": "Networking::Functional::ENS::Interrupt::Multimode_ChangeMTU_Unload", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Interrupt::SCP,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VDS_Function_ENS_Interrupt", "id": "05798333-9a19-48fa-a00d-adbf3070b667", "name": "Networking::Functional::ENS::Interrupt::SCP", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Interrupt::IPv4VGT,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VGT_Function_ENS_Interrupt", "id": "7e7ae85c-4672-491a-9877-7489312cd11a", "name": "Networking::Functional::ENS::Interrupt::IPv4VGT", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Interrupt::IPv6VGT,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VGT_Function_ENS_Interrupt", "id": "06a1feef-c2fa-4b1f-9a8e-bd36983fe466", "name": "Networking::Functional::ENS::Interrupt::IPv6VGT", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Interrupt::IPv6::Checksum.Offload,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VDS_Function_ENS_Interrupt_IPv6", "id": "36e341aa-9b7b-4be3-8830-2e3868563cac", "name": "Networking::Functional::ENS::Interrupt::IPv6::Checksum.Offload", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Interrupt::IPv6::Esxcli_DownUp,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VDS_Function_ENS_Interrupt_IPv6", "id": "2c45bc64-05ad-450a-9fa5-e18f22fcb346", "name": "Networking::Functional::ENS::Interrupt::IPv6::Esxcli_DownUp", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Interrupt::IPv6::FloodPing,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VDS_Function_ENS_Interrupt_IPv6", "id": "68468b68-7dd5-4615-8df0-c5dd0abc1693", "name": "Networking::Functional::ENS::Interrupt::IPv6::FloodPing", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Interrupt::IPv6::Iozone,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VDS_Function_ENS_Interrupt_IPv6", "id": "0a7c85ab-9ae1-4c64-aaa5-016bab0bc384", "name": "Networking::Functional::ENS::Interrupt::IPv6::Iozone", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Interrupt::IPv6::MultiCast,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VDS_Function_ENS_Interrupt_IPv6", "id": "f11a00d1-0eb6-4615-8dcb-4975181a928e", "name": "Networking::Functional::ENS::Interrupt::IPv6::MultiCast", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Interrupt::IPv6::Netperf,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VDS_Function_ENS_Interrupt_IPv6", "id": "4f4282ab-00db-4f43-ac9b-75ced8847043", "name": "Networking::Functional::ENS::Interrupt::IPv6::Netperf", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Interrupt::IPv6::SCP,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VDS_Function_ENS_Interrupt_IPv6", "id": "9f72015a-360f-4524-9e34-346010260c30", "name": "Networking::Functional::ENS::Interrupt::IPv6::SCP", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Interrupt::IPv6::TSO,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VDS_Function_ENS_Interrupt_IPv6", "id": "b3a742f1-0b86-417f-adf2-39527bdcfd8c", "name": "Networking::Functional::ENS::Interrupt::IPv6::TSO", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Interrupt::IPv6::VLAN_RX,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VDS_Function_ENS_Interrupt_IPv6", "id": "5a9179d0-bbfc-46e0-afb8-5aebb301e787", "name": "Networking::Functional::ENS::Interrupt::IPv6::VLAN_RX", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Interrupt::IPv6::VLAN_TX,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VDS_Function_ENS_Interrupt_IPv6", "id": "9095f64c-c072-436e-b463-73275519d328", "name": "Networking::Functional::ENS::Interrupt::IPv6::VLAN_TX", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Interrupt::IPv6::LRO,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VDS_Function_ENS_Interrupt_IPv6", "id": "04f7bbe0-de68-4ec1-90ed-0747a7503ad7", "name": "Networking::Functional::ENS::Interrupt::IPv6::LRO", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Interrupt::NoVM_IPv6JF,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_ENS_Interrupt_IPv6", "id": "3a0719a8-912c-4202-b7af-60274c97367e", "name": "Networking::Functional::ENS::Interrupt::NoVM_IPv6JF", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Interrupt::NoVM_IPv6Netperf,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_ENS_Interrupt_IPv6", "id": "50f9019f-2ff4-421f-b484-600c063e67b8", "name": "Networking::Functional::ENS::Interrupt::NoVM_IPv6Netperf", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Interrupt::NoVM_IPv6Ping,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_ENS_Interrupt_IPv6", "id": "fd6df155-b109-42cb-b6b7-a6da0edb66ce", "name": "Networking::Functional::ENS::Interrupt::NoVM_IPv6Ping", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Interrupt::NoVM_IPv6SCP,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_ENS_Interrupt_IPv6", "id": "b0c7b075-d2bd-47e8-a417-5b3be1b0bda9", "name": "Networking::Functional::ENS::Interrupt::NoVM_IPv6SCP", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Interrupt::NoVM_IPv6TSO,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_ENS_Interrupt_IPv6", "id": "220e330f-b00c-4ac4-9c9b-6f998e8c79fa", "name": "Networking::Functional::ENS::Interrupt::NoVM_IPv6TSO", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Interrupt::NoVM_IPv6VlanTag,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_ENS_Interrupt_IPv6", "id": "6d4d0219-a590-429e-a21d-bc3fd8a00f20", "name": "Networking::Functional::ENS::Interrupt::NoVM_IPv6VlanTag", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Interrupt::VMK_NFSReadWrite,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMKNIC_ENS_Interrupt", "id": "a56737ab-67e5-407f-9ba4-bce3b4d55577", "name": "Networking::Functional::ENS::Interrupt::VMK_NFSReadWrite", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Interrupt::VMK_TrafficIPv4,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMKNIC_ENS_Interrupt", "id": "8c5526a8-6134-4d67-99da-833fd4f5224a", "name": "Networking::Functional::ENS::Interrupt::VMK_TrafficIPv4", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Geneve::ENS::Functional::Interrupt::ChangeMTU,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_Geneve_Function_ENS_Interrupt", "id": "73feefd1-9631-4f11-9874-73ffa6d1ecb5", "name": "Networking::Geneve::ENS::Functional::Interrupt::ChangeMTU", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Geneve::ENS::Functional::Interrupt::Offload_IPv6,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_Geneve_Function_ENS_Interrupt", "id": "8565fb80-ad4b-4588-bea5-b67163180657", "name": "Networking::Geneve::ENS::Functional::Interrupt::Offload_IPv6", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Geneve::ENS::Functional::Interrupt::Uplink_Operate,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_Geneve_Function_ENS_Interrupt", "id": "ffed2006-6ec6-4f82-ae1a-2f085733b0d4", "name": "Networking::Geneve::ENS::Functional::Interrupt::Uplink_Operate", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Geneve::ENS::Functional::Interrupt::VMIPMAC_Change,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_Geneve_Function_ENS_Interrupt", "id": "c085a3d6-8b33-4154-8c73-6447fc88d1e2", "name": "Networking::Geneve::ENS::Functional::Interrupt::VMIPMAC_Change", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Geneve::ENS::Functional::Interrupt::RXFilter,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_Geneve_Function_ENS_Interrupt", "id": "1cf214ca-3680-4068-b0c2-e7afd70acc63", "name": "Networking::Geneve::ENS::Functional::Interrupt::RXFilter", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Geneve::ENS::Functional::Interrupt::IPv4VGT,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_Geneve_VGT_ENS_Interrupt", "id": "68e8c99b-9a21-4d1c-bf7e-65d5983c80b6", "name": "Networking::Geneve::ENS::Functional::Interrupt::IPv4VGT", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Geneve::ENS::Functional::Interrupt::Offload_Legacy_IPv4,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_Geneve_Function_ENS_Interrupt_Legacy", "id": "43957553-e525-4a8d-a1c0-9f631443015e", "name": "Networking::Geneve::ENS::Functional::Interrupt::Offload_Legacy_IPv4", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Geneve::ENS::Functional::Interrupt::Offload_Legacy_IPv6,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_Geneve_Function_ENS_Interrupt_Legacy", "id": "9bf60e56-bcde-4a00-8498-faf76fd733fa", "name": "Networking::Geneve::ENS::Functional::Interrupt::Offload_Legacy_IPv6", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Geneve::ENS::Stress::Interrupt::Mix_Various_Traffic,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VDS_Stress_ENS_Interrupt", "id": "cd7ba486-87e7-43da-a7cc-fce994fe8764", "name": "Networking::Geneve::ENS::Stress::Interrupt::Mix_Various_Traffic", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Stress::ENS::Interrupt::RandomOperationIO,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VDS_Stress_ENS_Interrupt", "id": "339f279d-54be-40c0-924d-943b78a1c8e6", "name": "Networking::Stress::ENS::Interrupt::RandomOperationIO", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Stress::ENS::Interrupt::Reboot,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VDS_Stress_ENS_Interrupt", "id": "a52c865b-4d3f-434f-8598-e06dd1591a46", "name": "Networking::Stress::ENS::Interrupt::Reboot", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Geneve::ENS::Stress::Interrupt::RandomOperationIO,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_Geneve_Stress_ENS_Interrupt", "id": "f0803638-e84d-4d34-84c1-8c88e141efa2", "name": "Networking::Geneve::ENS::Stress::Interrupt::RandomOperationIO", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Geneve::ENS::Stress::Interrupt::Traffic,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_Geneve_Stress_ENS_Interrupt", "id": "735f7bad-8059-4a6f-bf72-16f684d08910", "name": "Networking::Geneve::ENS::Stress::Interrupt::Traffic", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::SRIOV::PTP::Functional::TRX,Networking-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VS_Function_SRIOV_PTP", "id": "dc701882-ee18-4271-8535-191a847cb96c", "name": "Networking::SRIOV::PTP::Functional::TRX", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/ParamsLauncher.py Test::Failover::Parameters", "dependsOn": null, "group": "Failover::Parameters", "id": "493d0100-e196-437f-995d-4d0c7d2d0eb3", "name": "Test::Failover::Parameters", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 3}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::BasicFailover,Networking-LPTC", "dependsOn": ["Test::Failover::Parameters"], "group": "Failover::NW_TS_VS_Function", "id": "3c8b03f9-439f-4611-97a3-3eb1648c5719", "name": "Networking::Functional::BasicFailover", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::BeaconFailover,Networking-LPTC", "dependsOn": ["Test::Failover::Parameters"], "group": "Failover::NW_TS_VS_Function", "id": "fff1d11b-60d6-4ada-bd02-bb243937b0ca", "name": "Networking::Functional::BeaconFailover", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::IPv6::NicTeaming,Networking-LPTC", "dependsOn": ["Test::Failover::Parameters"], "group": "Failover::NW_TS_VS_Function_IPv6", "id": "f201fdd1-ce36-4d0b-b5dd-e58757d38339", "name": "Networking::Functional::IPv6::NicTeaming", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::VXLAN::Functional::Basic_DownUp,Networking-HPTC", "dependsOn": ["Test::Failover::Parameters"], "group": "Failover::NW_TS_VXLAN_Function", "id": "9398585a-8562-45af-838c-d2012d3d40d9", "name": "Networking::VXLAN::Functional::Basic_DownUp", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::VXLAN::Functional::Teaming,Networking-LPTC", "dependsOn": ["Test::Failover::Parameters"], "group": "Failover::NW_TS_VXLAN_Function", "id": "5f97745a-a1b9-4767-aa48-ebd02f3e6ae4", "name": "Networking::VXLAN::Functional::Teaming", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Geneve::Functional::Basic_DownUp,Networking-LPTC", "dependsOn": ["Test::Failover::Parameters"], "group": "Failover::NW_TS_Geneve_Function_VM", "id": "e8787677-5ce1-4457-bf8b-829199e70f24", "name": "Networking::Geneve::Functional::Basic_DownUp", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Geneve::Functional::Teaming,Networking-LPTC", "dependsOn": ["Test::Failover::Parameters"], "group": "Failover::NW_TS_Geneve_Function_VM", "id": "740c8f62-7886-437c-b7b9-3b78bf1a910f", "name": "Networking::Geneve::Functional::Teaming", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::MultipleUplink_Ops,Networking-LPTC", "dependsOn": ["Test::Failover::Parameters"], "group": "NW_TS_VS_MU_Function", "id": "02effe41-1898-48ba-a79e-dc30e5ac50fe", "name": "Networking::Functional::MultipleUplink_Ops", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::SRIOV::Functional::Disconnect_VF,Networking-HPTC", "dependsOn": ["Test::Failover::Parameters"], "group": "Failover::NW_TS_MIX_SRIOV", "id": "fe0f28d5-bd74-4264-85e3-0af39c7087a0", "name": "Networking::SRIOV::Functional::Disconnect_VF", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::NoVM_SpeedDuplex,Networking_NoVM-HPTC", "dependsOn": ["Test::Failover::Parameters"], "group": "Failover::NW_TS_VMK_VMK", "id": "f29506e5-2447-465c-80ba-cf1a7114f5f0", "name": "Networking::Functional::NoVM_SpeedDuplex", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::NoVM_DCBx_Check,Networking_NoVM-LPTC", "dependsOn": ["Test::Failover::Parameters"], "group": "Failover::NW_TS_VMK_VMK", "id": "7680cab3-a28e-47dd-8621-65bd5baad6e1", "name": "Networking::Functional::NoVM_DCBx_Check", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::NoVM_SwitchPortEnableDisable,Networking_NoVM-HPTC", "dependsOn": ["Test::Failover::Parameters"], "group": "Failover::NW_TS_VMK_VMK", "id": "6568bcd7-96de-4348-b4f6-fffc37a6fbf3", "name": "Networking::Functional::NoVM_SwitchPortEnableDisable", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::NoVM_DCBx_SwitchPort_LLDP,Networking_NoVM-HPTC", "dependsOn": ["Test::Failover::Parameters"], "group": "Failover::NW_TS_VMK_VMK", "id": "b419e73f-2fb6-450b-84ac-93bbc20c8ef6", "name": "Networking::Functional::NoVM_DCBx_SwitchPort_LLDP", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Geneve::Functional::LACP,Networking-LPTC", "dependsOn": ["Test::Failover::Parameters"], "group": "NW_TS_Geneve_Function_LACP", "id": "729fb962-55f9-4afd-ae33-d714e826a899", "name": "Networking::Geneve::Functional::LACP", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::NoVM_LACP,Networking_NoVM-LPTC", "dependsOn": ["Test::Failover::Parameters"], "group": "NW_TS_VMK_LACP_MIX_VDS", "id": "09190442-2205-45e0-8f97-d1dc9eea65ab", "name": "Networking::Functional::NoVM_LACP", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Polling::NoVM_SwitchPortEnableDisable,Networking_ENS-POLLING", "dependsOn": ["Test::Failover::Parameters"], "group": "Failover::NW_TS_VMK_VMK_ENS_Polling", "id": "a181ba65-957c-4928-875f-f700e19fd0e0", "name": "Networking::Functional::ENS::Polling::NoVM_SwitchPortEnableDisable", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Geneve::ENS::Functional::Polling::Teaming,Networking_ENS-POLLING", "dependsOn": ["Test::Failover::Parameters"], "group": "Failover::NW_TS_Geneve_Function_ENS_Polling", "id": "e1fb76f8-1d42-4afa-a5e7-88ede6dc235c", "name": "Networking::Geneve::ENS::Functional::Polling::Teaming", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Geneve::ENS::Functional::Polling::Basic_DownUp,Networking_ENS-POLLING", "dependsOn": ["Test::Failover::Parameters"], "group": "Failover::NW_TS_Geneve_Function_ENS_Polling", "id": "f75d2582-e8bc-4110-8717-e82932b9aab4", "name": "Networking::Geneve::ENS::Functional::Polling::Basic_DownUp", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Polling::NUMA_Teaming_Failover,Networking_ENS-POLLING", "dependsOn": ["Test::Failover::Parameters"], "group": "NW_TS_VDS_Function_ENS_NUMA_Polling", "id": "8d3a1b96-51bb-49d7-ac8a-b7f97af968c7", "name": "Networking::Functional::ENS::Polling::NUMA_Teaming_Failover", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Interrupt::NoVM_SwitchPortEnableDisable,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Failover::Parameters"], "group": "Failover::NW_TS_VMK_VMK_ENS_Interrupt", "id": "bc87d2f3-4e12-48f5-94b2-4780f6db5ffc", "name": "Networking::Functional::ENS::Interrupt::NoVM_SwitchPortEnableDisable", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Interrupt::NUMA_Teaming_Failover,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Failover::Parameters"], "group": "NW_TS_VDS_Function_ENS_NUMA_Interrupt", "id": "96be32e0-685f-417f-91fb-87c35f77ccb7", "name": "Networking::Functional::ENS::Interrupt::NUMA_Teaming_Failover", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Geneve::ENS::Functional::Interrupt::Basic_DownUp,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Failover::Parameters"], "group": "Failover::NW_TS_Geneve_Function_ENS_Interrupt", "id": "4fe7f103-91a1-4ce8-8725-6971debf097b", "name": "Networking::Geneve::ENS::Functional::Interrupt::Basic_DownUp", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Geneve::ENS::Functional::Interrupt::Teaming,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Failover::Parameters"], "group": "Failover::NW_TS_Geneve_Function_ENS_Interrupt", "id": "48fce6bd-9ef0-4abc-ae82-2eaa1ee9383a", "name": "Networking::Geneve::ENS::Functional::Interrupt::Teaming", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/ParamsLauncher.py Test::VC::Parameters", "dependsOn": null, "group": "VC::Parameters", "id": "aa727dda-49f0-4291-a3f0-844ade01edb9", "name": "Test::VC::Parameters", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 3}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::VMOPs,Networking-HPTC", "dependsOn": ["Test::VC::Parameters"], "group": "NW_TS_WithVC", "id": "a35a45bb-e137-4923-ad01-4eb9452ca66f", "name": "Networking::Functional::VMOPs", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::IPv6::VMOPs,Networking-LPTC", "dependsOn": ["Test::VC::Parameters"], "group": "NW_TS_WithVC_IPv6", "id": "3570b071-6a74-45b3-b1f5-fad28f3badab", "name": "Networking::Functional::IPv6::VMOPs", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Geneve::Functional::NoVM_EncapRxFilterTwoTuple,Networking-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_Geneve_Function_2Tuple", "id": "d210076d-ba35-401b-a05f-b3659269fd0c", "name": "Networking::Geneve::Functional::NoVM_EncapRxFilterTwoTuple", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 30}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Geneve::Functional::RXFilterTwoTuple,Networking-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_Geneve_Function_VM_2Tuple", "id": "7c4d93d5-4144-4492-8bb4-86e78d3b885c", "name": "Networking::Geneve::Functional::RXFilterTwoTuple", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 30}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::SRIOV::Functional::VMOPs,Networking-LPTC", "dependsOn": ["Test::VC::Parameters"], "group": "NW_TS_VS_Function_SRIOV_WithVC", "id": "6b09bbb7-8037-44f6-ab8f-0547227617d0", "name": "Networking::SRIOV::Functional::VMOPs", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/ParamsLauncher.py Test::Twohosts::VC::Parameters", "dependsOn": null, "group": "Twohosts::VC::Parameters", "id": "27f928bb-b05b-4d67-a3b0-dbd74412cd55", "name": "Test::Twohosts::VC::Parameters", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 3}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::VMOTION,Networking-HPTC", "dependsOn": ["Test::Twohosts::VC::Parameters"], "group": "NW_TS_TwoHosts_WithVC", "id": "43c5b51f-a663-4570-8ce1-29d5b481f164", "name": "Networking::Functional::VMOTION", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::SRIOV::Functional::PF_VMotion,Networking-LPTC", "dependsOn": ["Test::Twohosts::VC::Parameters"], "group": "NW_TS_MIX_SRIOV_TwoHosts_WithVC", "id": "03f7e0cd-6940-42d2-b07e-0bc8eec2189b", "name": "Networking::SRIOV::Functional::PF_VMotion", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/ParamsLauncher.py Test::Twohosts::Parameters", "dependsOn": null, "group": "Twohosts::Parameters", "id": "6191cf1f-94e8-4cc9-a6c3-e7b2fc607ea1", "name": "Test::Twohosts::Parameters", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 3}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::WOL,Networking-LPTC", "dependsOn": ["Test::Twohosts::Parameters"], "group": "NW_TS_TwoHosts_NoVC", "id": "cf8b4800-327c-45c2-bf2b-1d4936a92995", "name": "Networking::Functional::WOL", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/ParamsLauncher.py Test::Twohosts::Failover::Parameters", "dependsOn": null, "group": "Twohosts::Failover::Parameters", "id": "44eeaff7-372d-4b38-9126-fcfc66a97eaf", "name": "Test::Twohosts::Failover::Parameters", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 3}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py DDV::Functional::EIRandomNoReload,DDV-HPTC", "dependsOn": ["Test::Parameters"], "group": "DDV-HPTC", "id": "e22a3554-5ccf-4a99-8ebd-3b03017ddc0e", "name": "DDV::Functional::EIRandomNoReload", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py DDV::Functional::StopNoReload,DDV-HPTC", "dependsOn": ["Test::Parameters"], "group": "DDV-HPTC", "id": "6fb942a4-626e-42a9-a1c9-319926b044d6", "name": "DDV::Functional::StopNoReload", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/ParamsLauncher.py Test::IUVT::Parameters", "dependsOn": null, "group": "Parameters-IUVT", "id": "f43f6be8-96f7-43a4-abea-f7b71672cff2", "name": "Test::IUVT::Parameters", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 3}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Asyncdriver::Uninstall_Component,HPTC-AsyncDriver", "dependsOn": ["Test::IUVT::Parameters"], "group": "Uninstall_Component", "id": "1a116b58-7925-4e5c-b631-db542feb923b", "name": "Uninstall::Component", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 30}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Asyncdriver::Rollup_ISOTest,HPTC-AsyncDriver", "dependsOn": ["Test::IUVT::Parameters", "Networking::Functional::NoVM_NicAdapterInfo"], "group": "Rollup_ISOTest", "id": "095e29a4-bf46-4825-85be-629609d41db7", "name": "Asyncdriver::Rollup_ISOTest", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 30}], "unselectedTestList": [{"checked": true, "cmd": "/certs/common/Launcher80/driverlauncher.sh /certs/common/Launcher80/DriverUpgradeLauncher.py VUM-ISO-Upgrade", "dependsOn": null, "group": "Driver-Upgrade", "id": "ada31e51-fd4e-4469-bb1a-5e17553a5c6c", "name": "VUM-ISO-Upgrade", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 30}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::LRO_RSS,Networking-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VS_Function", "id": "2ff9b52a-2d28-49ba-98d6-84b0b72cb865", "name": "Networking::Functional::LRO_RSS", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Stress::NetqueueRSS,Networking-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VS_Stress_NetQRSS", "id": "ad4b23bb-ec85-4c96-a637-6d8565de2a12", "name": "Networking::Stress::NetqueueRSS", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::IPv6::LRO,Networking-LPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VS_Function_IPv6", "id": "f11c975a-299d-4eba-a8af-ee9997a448cc", "name": "Networking::Functional::IPv6::LRO", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Geneve::Functional::IPv4LRO,Networking-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_Geneve_Function_VM", "id": "974947d2-9e5c-4aa7-94d2-54fdaf6d2357", "name": "Networking::Geneve::Functional::IPv4LRO", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Geneve::Functional::IPv6LRO,Networking-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_Geneve_Function_VM", "id": "12acb7c6-aa2e-4e2a-9db2-530277b5579b", "name": "Networking::Geneve::Functional::IPv6LRO", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::SRIOV::Functional::DriverReloadWithSpecParams,Networking-LPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_MIX_SRIOV", "id": "9d9976f9-77c8-4402-a750-460f8a92902a", "name": "Networking::SRIOV::Functional::DriverReloadWithSpecParams", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::NoVM_VLAN_RX,Networking_NoVM-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK", "id": "4bc85b13-5950-450f-95c9-707634da86c4", "name": "Networking::Functional::NoVM_VLAN_RX", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::NoVM_IPv4LRO,Networking_NoVM-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK", "id": "3f268087-5493-4a5c-9010-bfee3360509c", "name": "Networking::Functional::NoVM_IPv4LRO", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::NoVM_IPv6LRO,Networking_NoVM-HPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_IPv6", "id": "f013fdb3-8912-48c2-b51b-bc26b4130440", "name": "Networking::Functional::NoVM_IPv6LRO", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::Netqueue_Latency,Networking-LPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VS_NetQ_Latency", "id": "53e4ee35-59fb-4ea1-b4bf-7d4d124690ba", "name": "Networking::Functional::Netqueue_Latency", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Stress::Rxfilter_Limit,Networking-LPTC", "dependsOn": ["Test::Parameters"], "group": "NW_TS_Multi_vNICs_STATIC", "id": "1cbfd43f-047a-418a-a40f-497cec7dcd83", "name": "Networking::Stress::Rxfilter_Limit", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Polling::NoVM_RSS,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_ENS_Polling", "id": "28d09780-836b-4fde-9146-8a9c3df84f61", "name": "Networking::Functional::ENS::Polling::NoVM_RSS", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Geneve::ENS::Functional::Polling::Offload_Legacy_IPv4,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_Geneve_Function_ENS_Polling_Legacy", "id": "452a629f-87c3-40f7-a7ab-c47637f76bfb", "name": "Networking::Geneve::ENS::Functional::Polling::Offload_Legacy_IPv4", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Geneve::ENS::Functional::Polling::Offload_Legacy_IPv6,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_Geneve_Function_ENS_Polling_Legacy", "id": "e005ca26-5601-4e22-a70e-9aa77a132352", "name": "Networking::Geneve::ENS::Functional::Polling::Offload_Legacy_IPv6", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Polling::Esxcli_DownUp,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VDS_Function_ENS_Polling", "id": "e766e1e3-a92d-409d-9d8c-06671d6f61f6", "name": "Networking::Functional::ENS::Polling::Esxcli_DownUp", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Polling::Multimode_ChangeMTU_Unload,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VDS_Function_ENS_Polling", "id": "de7cdf22-3122-45f3-8817-59c72d36ea0a", "name": "Networking::Functional::ENS::Polling::Multimode_ChangeMTU_Unload", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Polling::SCP,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VDS_Function_ENS_Polling", "id": "e3fb2cc3-a604-4d05-ae17-6f007947a414", "name": "Networking::Functional::ENS::Polling::SCP", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Polling::ChangeMTU_Reset,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VDS_Function_ENS_Polling", "id": "c59e21d9-f251-4d36-8830-59aff5bdf09e", "name": "Networking::Functional::ENS::Polling::ChangeMTU_Reset", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Polling::IPv4LRO,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VDS_Function_ENS_Polling", "id": "5782c188-b203-4a46-b2f3-a5f42793a93f", "name": "Networking::Functional::ENS::Polling::IPv4LRO", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Polling::LRO_RSS,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VDS_Function_ENS_Polling_LRO_RSS", "id": "303e6154-570a-4369-b6f6-6488484d74d3", "name": "Networking::Functional::ENS::Polling::LRO_RSS", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 30}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Stress::ENS::Polling::NetqueueRSS,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VDS_Stress_ENS_Netqueue", "id": "ee1edec4-3961-48c2-9273-2956ece18a64", "name": "Networking::Stress::ENS::Polling::NetqueueRSS", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Polling::IPv6::Esxcli_DownUp,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VDS_Function_ENS_Polling_IPv6", "id": "dcef16a1-bf88-45e5-80a7-a422524af7a1", "name": "Networking::Functional::ENS::Polling::IPv6::Esxcli_DownUp", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Polling::IPv6::LRO,Networking_ENS-POLLING", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VDS_Function_ENS_Polling_IPv6", "id": "a71d7c7c-a4f7-441a-8aa8-7f963717ea5f", "name": "Networking::Functional::ENS::Polling::IPv6::LRO", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Interrupt::NoVM_RSS,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VMK_VMK_ENS_Interrupt", "id": "2c0bd9bc-7ff8-4e32-8106-fc5d7b7eef40", "name": "Networking::Functional::ENS::Interrupt::NoVM_RSS", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Interrupt::Esxcli_DownUp,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VDS_Function_ENS_Interrupt", "id": "bb8c13df-7819-4886-9000-08c2ef2f8488", "name": "Networking::Functional::ENS::Interrupt::Esxcli_DownUp", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Interrupt::IPv4LRO,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VDS_Function_ENS_Interrupt", "id": "a6a72c33-e7af-47e2-bfd8-4d00f650b77e", "name": "Networking::Functional::ENS::Interrupt::IPv4LRO", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Functional::ENS::Interrupt::LRO_RSS,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VDS_Function_ENS_Interrupt_LRO_RSS", "id": "918a1178-5cf7-464e-8312-126988cbd34d", "name": "Networking::Functional::ENS::Interrupt::LRO_RSS", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 30}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Geneve::ENS::Functional::Interrupt::AddDeleteUplink,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_Geneve_Function_ENS_Interrupt", "id": "715351a8-0704-44a3-8cca-e48b1fdf6dbf", "name": "Networking::Geneve::ENS::Functional::Interrupt::AddDeleteUplink", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Geneve::ENS::Functional::Interrupt::Offload_IPv4,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_Geneve_Function_ENS_Interrupt", "id": "3a83d235-eeb9-4909-99a0-9200136647d2", "name": "Networking::Geneve::ENS::Functional::Interrupt::Offload_IPv4", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py Networking::Stress::ENS::Interrupt::NetqueueRSS,Networking_ENS-INTERRUPT", "dependsOn": ["Test::Parameters"], "group": "NW_TS_VDS_Stress_ENS_Interrupt_Netqueue", "id": "6b4d11a6-94a9-4f66-96d0-d154b6b5bfbc", "name": "Networking::Stress::ENS::Interrupt::NetqueueRSS", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}, {"checked": true, "cmd": "/certs/common/Launcher80/commonlauncher.sh /certs/common/Launcher80/IOVPLauncher80.py DDV::Functional::EILoading,DDV-HPTC", "dependsOn": ["Test::Parameters"], "group": "DDV-HPTC", "id": "9f91ee2c-e224-4a88-8101-d42a2a0f4d8b", "name": "DDV::Functional::EILoading", "requiredOnce": true, "skippableIfLastPass": true, "visible": true, "testType": null, "estimatedTime": 90}], "session": {"category": "iovp", "name": "orchestrator-test-800", "type": "nic-cert80", "uuid": "96e80823-2c8d-492b-9100-2d5917379d73", "runUuid": "1e779f09-e421-47b3-b461-986d3e0b05c3"}, "userSelection": {"NIC100G": "true", "IPv6Yes": "true", "productId": "49356", "NetdumpYes": "true", "FirmwareNetdumpYes": "false", "GENEVERxFilterYes": "true", "ENSS": "true", "DriverUpgrade_No": "true", "ENSYes": "true", "PTP": "true", "VXLANoffloadYes": "true", "UENSYes": "true", "Non-ENSY": "true", "IPv6HardwareOffloadYes": "false", "RSSYes": "true", "VXLANRxFilterYes": "true", "GeneveoffloadYes": "true", "NotCertified": "true", "SRIOV_YES": "true", "MultiRSSYes": "true", "APIUsageNo": "true", "Async_driver": "true"}, "agent": {"mode": null, "version": "6.0"}, "tmSysMode": "PROD", "preSignedURL": "https://storage.googleapis.com/vmwviva-prod-ui-bucket/tmp/twf8r13lzaiOzPYo?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=viva-namespace-admin%40spvivag1-vcf-viva.iam.gserviceaccount.com%2F20250610%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250610T042321Z&X-Goog-Expires=604800&X-Goog-SignedHeaders=content-type%3Bhost&X-Goog-Signature=a2714af377aa8fc12a3069b2a35c27cfb1bb816664eddca19adbe729f97e75bdcd2c563c161155954f83637ad6ded3c334c1ad9b602de10d36b2f0d765bb99b3c3413c837002c4c2b204bed45cfe50715f44821c9d57e6e8939df7cf52f6db44a85371ba0abdf56dde92bfc6e8cbfafdff20b1addc0c7455d1ea3e0ef3e7c4f6f8861beab42f9a4e77ac8ee62276302d67bebb492f6c6e5b8ac18631e6ae9ea8e56d54b6a80072c2f4e553260a607e485ac2d194240970c881851683455dc5757eb54773396ec015d0f0c2407aa2f8b87ccd94c5412914e8ca8d4fc1f7a8b1783e0f9c8b78f657bb9b5a92e6d002d5230af7c6af894e1773e08d39d4b0c1a2e6"}