#!/bin/bash

# Test script for remote access to the Cedori Test Orchestrator
# Run this script from your local machine to test remote access

# Unset proxy settings to avoid 307 redirects
unset http_proxy https_proxy HTTP_PROXY HTTPS_PROXY ftp_proxy FTP_PROXY no_proxy NO_PROXY

REMOTE_IP="************"
BACKEND_URL="http://$REMOTE_IP:8000"
FRONTEND_URL="http://$REMOTE_IP:3000"

echo "Testing remote access to Cedori Test Orchestrator"
echo "================================================="
echo "Remote IP: $REMOTE_IP"
echo "Backend URL: $BACKEND_URL"
echo "Frontend URL: $FRONTEND_URL"
echo ""

# Test backend API endpoints
echo "Testing Backend API..."
echo "----------------------"

echo "1. Testing /api/v1/setups/"
response=$(curl -s -w "HTTP_CODE:%{http_code}" "$BACKEND_URL/api/v1/setups/" -H "Content-Type: application/json")
http_code=$(echo "$response" | grep -o "HTTP_CODE:[0-9]*" | cut -d: -f2)
body=$(echo "$response" | sed 's/HTTP_CODE:[0-9]*$//')

if [ "$http_code" = "200" ]; then
    echo "✅ Setups endpoint working (HTTP $http_code)"
    echo "   Found $(echo "$body" | jq '. | length' 2>/dev/null || echo "N/A") setups"
else
    echo "❌ Setups endpoint failed (HTTP $http_code)"
    echo "   Response: $body"
fi

echo ""
echo "2. Testing /api/v1/agents/"
response=$(curl -s -w "HTTP_CODE:%{http_code}" "$BACKEND_URL/api/v1/agents/" -H "Content-Type: application/json")
http_code=$(echo "$response" | grep -o "HTTP_CODE:[0-9]*" | cut -d: -f2)
body=$(echo "$response" | sed 's/HTTP_CODE:[0-9]*$//')

if [ "$http_code" = "200" ]; then
    echo "✅ Agents endpoint working (HTTP $http_code)"
    echo "   Found $(echo "$body" | jq '. | length' 2>/dev/null || echo "N/A") agents"
else
    echo "❌ Agents endpoint failed (HTTP $http_code)"
    echo "   Response: $body"
fi

echo ""
echo "3. Testing /api/v1/servers/"
response=$(curl -s -w "HTTP_CODE:%{http_code}" "$BACKEND_URL/api/v1/servers/" -H "Content-Type: application/json")
http_code=$(echo "$response" | grep -o "HTTP_CODE:[0-9]*" | cut -d: -f2)
body=$(echo "$response" | sed 's/HTTP_CODE:[0-9]*$//')

if [ "$http_code" = "200" ]; then
    echo "✅ Servers endpoint working (HTTP $http_code)"
    echo "   Found $(echo "$body" | jq '. | length' 2>/dev/null || echo "N/A") servers"
else
    echo "❌ Servers endpoint failed (HTTP $http_code)"
    echo "   Response: $body"
fi

echo ""
echo "Testing Frontend..."
echo "-------------------"

# Test frontend accessibility
response=$(curl -s -w "HTTP_CODE:%{http_code}" "$FRONTEND_URL" -H "Accept: text/html")
http_code=$(echo "$response" | grep -o "HTTP_CODE:[0-9]*" | cut -d: -f2)

if [ "$http_code" = "200" ]; then
    echo "✅ Frontend accessible (HTTP $http_code)"
    echo "   You can open $FRONTEND_URL in your browser"
else
    echo "❌ Frontend not accessible (HTTP $http_code)"
fi

echo ""
echo "Summary"
echo "======="
echo "If all tests show ✅, you can:"
echo "1. Open $FRONTEND_URL in your local browser"
echo "2. Access the API documentation at $BACKEND_URL/docs"
echo "3. Use the Setup Management features without 307 redirect errors"
echo ""
echo "If any tests show ❌, check:"
echo "1. Services are running on the remote server (./start-dev.sh)"
echo "2. Firewall allows connections to ports 3000 and 8000"
echo "3. Proxy settings are unset (this script does it automatically)"
