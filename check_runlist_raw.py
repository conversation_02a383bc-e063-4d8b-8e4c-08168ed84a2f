#!/usr/bin/env python3
"""Check raw runlist content on agent."""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.ssh_executor import SSHExecutor

def check_runlist_raw():
    """Get raw runlist content."""
    agent_ip = "**************"
    username = "root"
    password = "vmware"
    
    print(f"\n📥 Checking runlist on VIVa agent at {agent_ip}")
    
    try:
        with SSHExecutor(hostname=agent_ip, username=username, password=password) as ssh:
            # First check file size
            stdout, stderr, exit_code = ssh.execute_command("ls -lh /vmware/input/runlist.json")
            print(f"\n📊 File info: {stdout.strip()}")
            
            # Get first 100 lines of the file
            print("\n📄 First 100 lines of runlist.json:")
            stdout, stderr, exit_code = ssh.execute_command("head -100 /vmware/input/runlist.json")
            print(stdout)
            
            # Check if it's valid JSON
            print("\n🔍 Checking JSON validity:")
            stdout, stderr, exit_code = ssh.execute_command("python3 -m json.tool /vmware/input/runlist.json > /dev/null 2>&1 && echo 'Valid JSON' || echo 'Invalid JSON'")
            print(f"   {stdout.strip()}")
            
            # Count test entries
            print("\n📊 Counting test entries:")
            stdout, stderr, exit_code = ssh.execute_command("grep -c '\"name\"' /vmware/input/runlist.json || echo '0'")
            print(f"   Found {stdout.strip()} name entries")
            
            # Look for test sections
            stdout, stderr, exit_code = ssh.execute_command("grep -A5 -B5 -i 'test' /vmware/input/runlist.json | head -50")
            if stdout:
                print("\n🔍 Test-related content:")
                print(stdout)
                
    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_runlist_raw()