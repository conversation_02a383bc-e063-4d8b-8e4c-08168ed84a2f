# Changelog

All notable changes to the Cedori Test Orchestrator project will be documented in this file.

## [Unreleased]

### Changed
- **BREAKING**: Migrated from Playwright web scraping to SSH-based execution
  - Replaced Playwright with <PERSON><PERSON><PERSON> for agent communication
  - Now uses direct SSH access to execute CLI commands on VIVa agents
  - Tests are executed using runlist.json files via AgentLauncher CLI
  - More reliable, faster, and resource-efficient than web scraping

### Added
- SSH Executor Service (`app/services/ssh_executor.py`)
  - Handles SSH connections, file transfers, and command execution
  - Supports both sync and async interfaces
  - Manages runlist upload/download and test execution
  
- Runlist Management System
  - New `Runlist` database model for storing test configurations
  - Comprehensive API endpoints for runlist CRUD operations
  - Predefined templates for common test scenarios
  - Support for runlist execution and result tracking

- New API Endpoints
  - `/api/v1/runlists` - Runlist management
  - `/api/v1/runlists/templates` - Access predefined templates
  - `/api/v1/runlists/{id}/execute` - Execute runlist on setup

### Removed
- Playwright dependency and all browser automation code
- Chrome/Chromium installation from Docker image
- Web scraping configuration options

### Updated
- Base scraper now uses SSH connections instead of browser automation
- Agent scraper converted to use SSH commands
- Celery tasks updated for runlist-based execution
- Docker image simplified (removed browser dependencies)
- Configuration updated with SSH-specific settings

## [1.0.0] - 2025-01-06

### Added
- Initial release with Playwright-based web scraping
- Multi-setup management for 40-50 test environments
- Real-time test progress monitoring
- Celery-based asynchronous task execution
- PostgreSQL database for persistence
- Redis for caching and task queue
- React frontend with Material-UI